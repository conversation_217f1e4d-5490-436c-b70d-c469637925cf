#!/usr/bin/env python3
"""
测试新的流式更新架构
验证预插入机制和实时持久化功能
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.agent.api_query_processor import APIQueryProcessor
from src.services.feishu.query_processor import FeishuQueryProcessor
from src.services.chatbot.history_service import get_conversation_history_as_input_list, save_user_message
from src.utils.logger import logger

# 定义多个测试查询，覆盖所有agent
test_queries = [
    {
        "query": "杭州市昨天新增多少新注册门店？",
        "description": "测试sales_order_analytics - 新门店注册分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "为什么客户 TeaBaby茶宝宝 (ID: 559867) 不算拉新？",
        "description": "测试sales_order_analytics - 有效拉新判断",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "分析一下杭州市的客户昨天PB商品销售额",
        "description": "测试sales_order_analytics - PB商品销售额分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "查询大客户ID=1132155的门店，本月份的订单明细。列出大客户名字、门店名字和ID、门店地址、订单号、订单金额、商品名字、商品SKU、商品金额、商品件数、下单日",
        "description": "测试sales_order_analytics - 订单明细查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "查询大客户ID=1132155的门店，本月份的履约订单明细",
        "description": "测试sales_order_analytics - 履约订单明细查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "帮我查一下SKU是 '三麟苏打汽水' 的商品最新的质检报告链接",
        "description": "测试warehouse_and_fulfillment - 质检报告查询",
        "expected_agent": "warehouse_and_fulfillment",
    },
    {
        "query": "对比一下'象牌苏打水', '三麟苏打汽水'在过去6个月的销售情况",
        "description": "测试sales_order_analytics - 销售额对比分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "M1 李钱程团队本月的绩效分析",
        "description": "测试sales_kpi_analytics - 销售团队绩效分析",
        "expected_agent": "sales_kpi_analytics",
    },
    {
        "query": "安佳淡奶油在嘉兴仓的库存分析",
        "description": "测试warehouse_and_fulfillment - 库存分析",
        "expected_agent": "warehouse_and_fulfillment",
    },
    {
        "query": "如何申请DMS账户",
        "description": "测试general_chat_bot - 知识问答",
        "expected_agent": "general_chat_bot",
    },
]


def test_api_streaming():
    """测试API流式更新架构"""
    print("=" * 60)
    print("测试API流式更新架构")
    print("=" * 60)

    # 创建测试数据
    test_user_info = {
        "name": "test_user",
        "email": "<EMAIL>",
        "open_id": "test_open_id",
    }

    # 创建API处理器
    processor = APIQueryProcessor()

    all_tests_passed = True

    for i, test_case in enumerate(test_queries, 1):
        print(f"\n{'=' * 80}")
        print(f"测试查询 {i}/{len(test_queries)}: {test_case['description']}")
        print(f"查询内容: {test_case['query']}")
        print(f"预期调用agent: {test_case['expected_agent']}")
        print("=" * 80)

        test_conversation_id = f"test_conv_{int(time.time())}_{i}"

        try:
            # 保存用户查询到chat_history
            save_success = save_user_message(
                username=test_user_info["name"],
                email=test_user_info["email"],
                conversation_id=test_conversation_id,
                content=test_case["query"]
            )
            if save_success:
                print(f"✓ 用户查询已保存到chat_history: {test_case['query'][:50]}...")
            else:
                print(f"⚠ 保存用户查询失败: {test_case['query'][:50]}...")

            # 执行流式查询
            print("开始执行流式查询...")
            response_count = 0
            agent_detected = False

            for response in processor.run_query(
                user_query=test_case["query"],
                user_info=test_user_info,
                conversation_id=test_conversation_id,
            ):
                response_count += 1

                # 检测是否调用了预期的agent
                if test_case["expected_agent"] in response:
                    agent_detected = True
                    print(f"✓ 检测到预期agent: {test_case['expected_agent']}")

                if response_count <= 3:  # 只显示前3个响应
                    print(f"响应 {response_count}: {response[:80]}...")
                elif response_count == 4:
                    print("... (更多响应)")

            print(f"总共收到 {response_count} 个流式响应")

            # 验证数据库中的记录
            history = get_conversation_history_as_input_list(
                test_user_info["name"], test_user_info["email"], test_conversation_id
            )

            print(f"对话历史记录数量: {len(history)}")

            # 检查测试结果
            if response_count > 0 and len(history) > 0 and agent_detected:
                print(f"✓ 测试查询 {i} 通过")
            else:
                print(f"✗ 测试查询 {i} 失败")
                all_tests_passed = False

        except Exception as e:
            print(f"✗ 测试查询 {i} 异常: {e}")
            logger.exception(f"测试查询 {i} 异常")
            all_tests_passed = False

    return all_tests_passed


async def test_feishu_streaming():
    """测试飞书流式更新架构"""
    print("=" * 60)
    print("测试飞书流式更新架构")
    print("=" * 60)

    # 创建测试数据
    test_user_info = {
        "name": "feishu_test_user",
        "email": "<EMAIL>",
        "open_id": "feishu_test_open_id",
    }

    test_message_id = f"test_msg_{int(time.time())}"
    test_query = "请分析一下用户行为数据"

    print(f"测试消息ID: {test_message_id}")
    print(f"测试查询: {test_query}")

    try:
        # 注意：这里只测试处理器的创建，不执行完整的飞书流程
        # 因为完整的飞书流程需要真实的飞书环境
        processor = FeishuQueryProcessor()

        print("飞书处理器创建成功")
        print("注意：完整的飞书测试需要真实的飞书环境")

        return True

    except Exception as e:
        print(f"飞书测试失败: {e}")
        logger.exception("飞书测试异常")
        return False


def test_streaming_threshold():
    """测试流式更新阈值设置"""
    print("=" * 60)
    print("测试流式更新阈值设置")
    print("=" * 60)

    from src.services.agent.base_query_processor import STREAMING_UPDATE_THRESHOLD

    print(f"当前流式更新阈值: {STREAMING_UPDATE_THRESHOLD} 字符")

    if STREAMING_UPDATE_THRESHOLD == 15:
        print("✓ 阈值已正确设置为15字符")
        return True
    else:
        print(f"✗ 阈值应该是15字符，但当前是{STREAMING_UPDATE_THRESHOLD}字符")
        return False


def main():
    """主测试函数"""
    print("开始测试新的流式更新架构")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    test_results = []

    test_count = len(test_queries)

    # 测试1: 流式更新阈值
    print("\n" + "=" * 80)
    result1 = test_streaming_threshold()
    test_results.append(("流式更新阈值", result1))

    # 测试2: API流式更新（包含多个功能测试查询）
    print("\n" + "=" * 80)
    result2 = test_api_streaming()
    test_results.append(("API流式更新（{}个功能查询）".format(test_count), result2))

    # 测试3: 飞书流式更新
    print("\n" + "=" * 80)
    result3 = asyncio.run(test_feishu_streaming())
    test_results.append(("飞书流式更新", result3))

    # 汇总测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试都通过了！新的流式更新架构工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关问题。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
