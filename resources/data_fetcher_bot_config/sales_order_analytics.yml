agent_name: sales_order_analytics
model_provider: xm
model: kimi-k2
model_settings:
  temperature: 0.1
  extra_body: {"provider": {"sort": "throughput"}}
need_system_prompt: true
tools:
  - name: fetch_mysql_sql_result
  - name: get_table_sample_data
  - name: get_sales_manager_team_members
  - name: fetch_ddl_for_table
  - name: get_large_areas
  - name: get_new_customers_of_today
  - name: get_high_valued_customers_starting_from_now
  - name: sku_price_tool
agent_description: sales_order_analytics.md
agent_tables:
  - name: orders
    desc: 订单表，记录所有订单的基本信息，包括订单状态、门店ID(m_id)、门店所属的运营服务区(area_no)、订单配送信息等
  - name: order_item
    desc: 订单明细表，记录订单中每个商品的详细信息，包括商品SKU(sku)、商品名字(pd_name)、购买数量(amount)、单价、实际支付总价等
  - name: merchant
    desc: 商户信息主表。存储了商户的名字(mname)、注册地址以及省市区、手机号、所属的大客户admin_id（如有）、所属的运营服务区编号(area_no)等核心信息。
  - name: merchant_sub_account
    desc: 商户子账号表，记录商户子账号的基本信息，包括子账号ID(account_id)、子账号名字(contact)、子账号的最后登录时间(login_time)
  - name: admin
    desc: 大客户表(admin_type=0)，记录大客户ID(admin_id)、大客户名字(name_remakes)、大客户所属的销售员ID(saler_id)等
  - name: area
    desc: 运营服务区的基本信息，包括运营服务区编码(area_no)、运营服务区名字、运营服务区所属的大区编码(large_area_no)等
  - name: large_area
    desc: 大区表，记录运营服大区的基本信息，包括大区编码(large_area_no)、大区名字(large_area_name)等
  - name: products
    desc: 商品SPU表，记录商品的基本信息，包括商品ID(pd_id)、商品名字(pd_name)、商品后端类目ID(category_id)等
  - name: inventory
    desc: 商品SKU表，记录商品的详细信息，包括商品SKU(sku)、商品SPU ID(pd_id)、商品产地、商品规格(weight)等
  - name: category
    desc: 商品后端类目表，记录商品的类目信息，包括类目ID(id)、类目名字(category)、类目类型(type，=4表示水果类目)等
  - name: crm_bd_org
    desc: 销售组织架构表，记录销售人员的基本信息，包括销售人员ID(bd_id)、销售人员名字(bd_name)、销售人员所属的上级主管名字(parent_name)等
  - name: follow_up_relation
    desc: 商户销售私海关系表(reassign=0表示私海客户)，记录商户ID(m_id)、商户所属的销售员ID(admin_id)、商户所属的销售员名字(admin_name)等
  - name: follow_up_record
    desc: 记录商户被拜访的记录(有时也叫打卡记录），包括商户ID(m_id)、拜访人ID(admin_id)、拜访人名字(admin_name)、商户所属的运营服务区编码(area_no)等
  - name: after_sale_order
    desc: 售后单表，记录订单的售后申请的基本信息，包括售后订单编号(after_sale_order_no)、售后门店ID(m_id)、原订单编号(order_no)、售后商品SKU(sku)、售后状态(status)等
  - name: after_sale_proof
    desc: 售后明细表，记录售后单的处理明细情况，包括售后数量(quantity)、售后凭证图片(proof_pic)、最终售后金额(handle_num)、客服审核备注(apply_remark)等
  - name: delivery_plan
    desc: 订单配送、履约计划表，记录订单的配送信息，包括配送日期(delivery_time)、配送状态(status)、配送商品数量(quantity)等，一定要区分省心送订单和普通订单的履约件数统计逻辑
  - name: contact
    desc: 联系人收货地址表，存储商户的收货地址信息，每个contact_id代表一个配送点位。关联查询`merchant`.`m_id`和`delivery_plan`.`contact_id`，包含联系人信息、详细地址、配送仓库编号(store_no)、距离仓库距离(distance)等
  - name: shopping_cart
    desc: 门店的购物车，记录门店的购物车信息，包括门店id(m_id)、子账号id(account_id)、商品sku(sku)、商品数量(quantity)等
  - name: products_property_value
    desc: 商品属性值表，主要用来记录商品的品牌信息，包括商品ID(pd_id)、商品品牌名称(products_property_value, 当且仅当products_property_id=2时，表示商品的品牌)
  - name: merchant_label
    desc: 客户标签表，记录每个客户的标签。例如：过年营业、过年不营业、校区商户、非校区商户等
  - name: merchant_coupon
    desc: 商户领取的优惠券表，记录每个商户领取的优惠券信息，包括商户ID(m_id)、优惠券ID(coupon_id)、优惠券面值、优惠券有效期(vaild_date)等
  - name: front_category
    desc: 前端(商城)类目表，前端类目是指商城展示的类目，比如冷冻蛋糕、烘焙辅料、今日推荐等。通常销售人员只能看到前端类目，不能看到后端类目，所以他们提到的类目通常是指前端类目。
  - name: merchant_leads
    desc: 主要是用来判断门店是否自主注册，还是扫BD推荐码注册的。
  - name: product_cost
    desc: 商品成本管理表，记录各仓库商品的当前成本和历史成本信息，包括仓库编号(warehouse_no)、SKU编码(sku)、当前成本(current_cost)、上期成本(previous_cost)等。注意：成本数据属于敏感信息，销售人员(BD、M1、M2)无权限查看。
  - name: area_sku
    desc: 运营服务区商品销售配置表，记录每个商品在各运营服务区的上架状态、销售价格和客户类型限制，包括SKU编码(sku)、运营服务区编号(area_no)、销售价格(price)、上架状态(on_sale)、客户类型限制(m_type)等。
agent_as_tool_description: |
  这是一个专门用于销售订单深度分析的AI机器人，基于19个核心业务表提供全方位的销售数据分析服务。

  **核心销售分析能力：**
  - 统计指定时间段的销售总额、订单量、商品销售表现，支持按日/周/月等维度分析
  - 多维度数据透视：按运营服务区、商户、SKU/SPU、后端类目、前端类目等维度进行销售数据切片分析
  - 售后影响分析：计算售后率（已到货售后金额/总下单金额×100%），分析售后对销售的影响
  - 活跃用户统计：基于商户子账号最后登录时间分析用户活跃度

  **专业业务场景分析：**
  - 销售团队管理：统计销售人员（BD）私海客户业绩，支持按销售代表、销售主管、销售经理等层级进行团队业绩分析
  - 高价值客户识别：自动识别月履约金额超过2000元且购买4种以上商品的高价值客户
  - 品牌销售对比：PB品牌（C味、Protag蛋白标签、SUMMERFARM、ZILIULIU、沐清友、澄善、酷盖、鲜沐农场）vs NB品牌销售数据对比
  - 重点商品分析：AT商品（安佳、铁塔两大品牌）vs非AT商品的销售表现对比分析

  **履约与配送分析：**
  - 履约数据统计：区分省心送订单和普通订单的履约件数统计，计算履约GMV
  - 配送效率分析：基于delivery_plan表分析配送完成情况（status=6表示履约完成）
  - 区域配送表现：按运营服务区和大区维度分析配送效率和销售表现

  **商品与类目分析：**
  - 全品类商品分析：针对代销商品（inventory.sub_type in (1,2)）进行销售统计
  - 标品vs鲜果销售对比：区分鲜果类目（category.type=4）和标品的销售表现
  - 自营品履约分析：专门针对鲜沐自营品（inventory.sub_type=3）的履约实付金额统计
  - 成本与毛利分析：基于product_cost表分析商品成本价格，计算销售毛利率（销售人员无权限）
  - 区域定价策略查询：基于area_sku表查询不同运营服务区的商品定价

  **客户关系管理：**
  - 商户购买行为分析：分析商户注册时间、购买频次、客单价等关键指标
  - 客户标签分析：基于merchant_label表分析不同标签客户的购买行为
  - 拜访记录统计：通过follow_up_record表分析销售人员的客户拜访情况

  **特殊场景处理：**
  - 虚拟商品订单：自动识别奶油黄金卡订单（orders.type=10），无需关联实物商品
  - 购物车分析：分析门店购物车数据，了解客户购买意向
  - 门店商品价格查询：通过sku_price_tool查询门店商品优惠后的价格
  - 门店商品推荐：通过分析门店的主营业务，推荐我司售卖的商品

  该机器人具备强大的SQL查询能力，能够处理复杂的多表关联查询，为销售管理、客户分析、商品运营等提供精准的数据支持。
