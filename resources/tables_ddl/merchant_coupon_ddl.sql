CREATE TABLE `xianmudb`.`coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `name` varchar(255) DEFAULT NULL COMMENT '优惠券名称',
  `code` varchar(36) DEFAULT NULL COMMENT '优惠券代码',
  `money` decimal(10,2) DEFAULT NULL COMMENT '优惠金额',
  `threshold` decimal(10,2) DEFAULT '0.00' COMMENT '使用门槛金额',
  `type` tinyint(2) DEFAULT '0' COMMENT '优惠券类型，0:固定时间间隔到期, 1:固定时间点到期',
  `vaild_date` datetime DEFAULT NULL COMMENT '固定时间点到期时的有效日期',
  `vaild_time` int(11) DEFAULT NULL COMMENT '固定时间间隔到期时的有效时间，代表领取后多少天内有效。单位：天',
  `grouping` int(11) DEFAULT '0' COMMENT '卡券分组: 0-平台活动券, 1-售后补偿券, 2-区域拉新券, 3-会员权益券, 4-销售客情券, 5-销售月活券, 6-行业活动券, 8-员工福利券, 9-销售囤货券, 10-区域活动券, 11-销售品类券, 20-市场活动券, 13-销售现货券, 19-区域召回券, 21-其他, 16-功能测试券, 17-平台补偿券, 18-配送补偿券',
  `new_hand` tinyint(1) DEFAULT '0' COMMENT '是否仅限新手使用，0:否, 1:是',
  `category_id` text COMMENT '可使用的商品后端类目ID列表，逗号分隔',
  `sku` text COMMENT '可使用的商品SKU列表，逗号分隔',
  `add_time` datetime DEFAULT NULL COMMENT '创建时间',
  `status` int(11) DEFAULT '1' COMMENT '状态，1:存在, 2:已删除',
  `agio_type` int(11) NOT NULL DEFAULT '1' COMMENT '优惠类型: 1-普通商品优惠券, 2-普通运费优惠券, 3-精准送优惠券, 4-红包, 5-商品兑换券',
  `start_date` datetime DEFAULT NULL COMMENT '开始生效时间',
  `start_time` int(11) DEFAULT NULL COMMENT '开始生效间隔日期',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `limit_flag` int(11) DEFAULT NULL COMMENT '是否限制发放一个，0:否, 1:是',
  `activity_scope` int(11) DEFAULT NULL COMMENT '活动范围: 1:预售尾款, 2:除省心送/秒杀/预售外的活动可用, 3:全部, 4:仅省心送',
  `task_tag` tinyint(3) unsigned DEFAULT '0' COMMENT '任务标识，0:否, 1:是',
  `delete_tag` tinyint(3) unsigned DEFAULT '0' COMMENT '任务作废标识，0:否, 1:是',
  `auto_created` tinyint(4) DEFAULT '0' COMMENT '是否系统自动创建，0:否, 1:是',
  `quantity_claimed` int(11) DEFAULT '0' COMMENT '领取次数限制，0:不限, 大于0为实际限制次数',
  `grant_amount` int(10) unsigned DEFAULT NULL COMMENT '剩余发放总量',
  `grant_limit` int(11) DEFAULT '0' COMMENT '每人领取限制，0:不限, 大于0为实际限制张数',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  KEY `idx_coupon_status_agio_grouping` (`status`,`agio_type`,`grouping`),
  KEY `idx_coupon_name` (`name`),
  KEY `idx_coupon_agio_threshold_money` (`agio_type`,`threshold`,`money`)
) ENGINE=InnoDB AUTO_INCREMENT=132237 DEFAULT CHARSET=utf8 COMMENT='优惠券信息表';

CREATE TABLE `xianmudb`.`merchant_coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商户优惠券关联ID',
  `m_id` bigint(30) NOT NULL COMMENT '商户ID，关联merchant.m_id',
  `coupon_id` int(11) NOT NULL COMMENT '优惠券ID，关联coupon.id',
  `vaild_date` datetime DEFAULT NULL COMMENT '优惠券失效日期。默认情况下，仅需查询未过期的优惠券，即vaild_date > NOW()的记录。',
  `sender` varchar(36) DEFAULT NULL COMMENT '优惠券发送者',
  `used` tinyint(2) DEFAULT '0' COMMENT '是否已使用，0:未使用, 1:已使用',
  `add_time` datetime DEFAULT NULL COMMENT '领取时间',
  `order_no` varchar(36) DEFAULT NULL COMMENT '使用优惠券的订单号，关联orders.order_no',
  `receive_type` tinyint(4) DEFAULT NULL COMMENT '领取类型: 0-发放(人工), 1-手动领取, 2-抽奖活动(被动), 3-自动领取(新人注册/推荐好友下单), 4-满返活动(被动), 5-其他',
  `start_time` datetime DEFAULT NULL COMMENT '优惠券生效时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `send_id` bigint(20) DEFAULT NULL COMMENT '优惠券发放记录ID',
  `related_id` bigint(20) DEFAULT NULL COMMENT '优惠活动关联ID(如满返活动ID)',
  PRIMARY KEY (`id`),
  KEY `idx_merchant_coupon_order_no` (`order_no`),
  KEY `idx_merchant_coupon_coupon_id` (`coupon_id`),
  KEY `idx_merchant_coupon_send_id_used` (`send_id`,`used`),
  KEY `idx_merchant_coupon_valid_date` (`valid_date`),
  KEY `idx_merchant_coupon_add_time` (`add_time`),
  KEY `idx_merchant_coupon_mid_used_valid_start` (`m_id`,`used`,`valid_date`,`start_time`)
) ENGINE=InnoDB AUTO_INCREMENT=19873303 DEFAULT CHARSET=utf8 COMMENT='商户领取的优惠券列表。默认情况下，如果用户没有特别说明，仅需查询未过期的优惠券，即vaild_date > NOW()的记录。';