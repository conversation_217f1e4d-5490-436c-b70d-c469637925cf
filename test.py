import json

import requests
from requests.auth import HTTP<PERSON>asic<PERSON>uth


def search_xianmu_product_directly_from_es(
    query,
    es_host="es-cn-i7m2pv3ht000o90dy.public.elasticsearch.aliyuncs.com:9200",
    es_index="summerfarm_item_info",
    brand_name=None,
    size=60,
    minimum_score=20.0,
    city="杭州",
):
    """
    直接从Elasticsearch搜索鲜沐商品。

    :param query: 搜索关键词。
    :param es_host: Elasticsearch主机地址，默认为公网地址。
    :param es_index: Elasticsearch索引名称，默认为summerfarm_item_info。
    :return: Elasticsearch搜索结果，JSON格式。
    """
    area_no = 1001
    es_url = f"http://{es_host}/{es_index}/_search"
    auth = HTTPBasicAuth("elastic", "elastic@Xianmu0619")
    headers = {"Content-Type": "application/json"}
    must = [
        {"term": {"targetId": area_no}},
        {"terms": {"subType": [1, 2, 3, 4]}},  # 添加 subType 的 must 查询
    ]
    if brand_name is not None:
        must.append(
            {"term": {"brandName": brand_name}}
        )  # 这里应该是 brand_name 而不是 query
    search_body = {
        "from": 0,
        "size": size,
        "query": {
            "bool": {
                "filter": [
                    {"term": {"targetId": {"value": 1001, "boost": 1.0}}},
                    {"term": {"onSale": {"value": 1, "boost": 1.0}}},
                    {"term": {"deleteFlag": {"value": 1, "boost": 1.0}}},
                    {"term": {"marketItemDeleteFlag": {"value": 1, "boost": 1.0}}},
                    {"term": {"show": {"value": 1, "boost": 1.0}}},
                    {"term": {"onSaleStrategyType": {"value": 3, "boost": 1.0}}},
                ],
                "should": [
                    {
                        "match": {
                            "title": {
                                "query": query,
                                "operator": "OR",
                                "prefix_length": 0,
                                "max_expansions": 50,
                                "fuzzy_transpositions": True,
                                "lenient": False,
                                "zero_terms_query": "NONE",
                                "auto_generate_synonyms_phrase_query": True,
                                "boost": 120.0,
                            }
                        }
                    },
                    {
                        "match": {
                            "brandName": {
                                "query": query,
                                "operator": "OR",
                                "fuzziness": "2",
                                "prefix_length": 0,
                                "max_expansions": 50,
                                "fuzzy_transpositions": True,
                                "lenient": False,
                                "zero_terms_query": "NONE",
                                "auto_generate_synonyms_phrase_query": True,
                                "boost": 80.0,
                            }
                        }
                    },
                    {
                        "match": {
                            "propertyValues": {
                                "query": query,
                                "operator": "OR",
                                "prefix_length": 0,
                                "max_expansions": 50,
                                "fuzzy_transpositions": True,
                                "lenient": False,
                                "zero_terms_query": "NONE",
                                "auto_generate_synonyms_phrase_query": True,
                                "boost": 30.0,
                            }
                        }
                    },
                    {
                        "multi_match": {
                            "query": query,
                            "fields": [
                                "category.name^60.0",
                                "category.parentName^40.0",
                            ],
                            "type": "best_fields",
                            "operator": "OR",
                            "slop": 0,
                            "prefix_length": 0,
                            "max_expansions": 50,
                            "zero_terms_query": "NONE",
                            "auto_generate_synonyms_phrase_query": True,
                            "fuzzy_transpositions": True,
                            "boost": 1.0,
                        }
                    },
                    {
                        "match": {
                            "keyProperty": {
                                "query": query,
                                "operator": "OR",
                                "prefix_length": 0,
                                "max_expansions": 50,
                                "fuzzy_transpositions": True,
                                "lenient": False,
                                "zero_terms_query": "NONE",
                                "auto_generate_synonyms_phrase_query": True,
                                "boost": 30.0,
                            }
                        }
                    },
                    {
                        "match": {
                            "titlePure": {
                                "query": query,
                                "operator": "OR",
                                "prefix_length": 0,
                                "max_expansions": 50,
                                "fuzzy_transpositions": True,
                                "lenient": False,
                                "zero_terms_query": "NONE",
                                "auto_generate_synonyms_phrase_query": True,
                                "boost": 30.0,
                            }
                        }
                    },
                    {
                        "match": {
                            "titleExt": {
                                "query": query,
                                "operator": "OR",
                                "prefix_length": 0,
                                "max_expansions": 50,
                                "fuzzy_transpositions": True,
                                "lenient": False,
                                "zero_terms_query": "NONE",
                                "auto_generate_synonyms_phrase_query": True,
                                "boost": 10.0,
                            }
                        }
                    },
                ],
                "adjust_pure_negative": True,
                "minimum_should_match": "2",
                "boost": 1.0,
            }
        },
        "min_score": minimum_score,
        "sort": [{"_score": {"order": "desc"}}],
    }

    try:
        print(f"search_body:\n{search_body}")
        response = requests.post(
            es_url, auth=auth, headers=headers, data=json.dumps(search_body), timeout=50
        )
        response.raise_for_status()  # 检查请求是否成功
        hits = response.json().get("hits", {}).get("hits", [])
        # print(hits)
        result_list = []
        for hit in hits:
            source = hit.get("_source", {})
            result = {}
            sku = source.get("itemCode", "")
            result["_score"] = hit.get("_score", "")
            result["area_price"] = f"{city}, ¥{source['price']}"
            result["sku_id"] = f"{sku}, {source.get('specification', '')}"
            result["sku"] = sku
            result["spu_name"] = source.get("title", "")
            result["pd_id"] = source.get(
                "marketItemId", source.get("id", "")
            )  # 优先使用marketItemId, 否则使用id
            picture_path = source.get("mainPicture", "404.jpg")
            result["img_url"] = (
                picture_path
                if picture_path.startswith("http")
                else "https://azure.summerfarm.net/" + picture_path
            )
            result["brand"] = source.get("brandName", "")
            result["weight"] = source.get("specification", "")
            result["properties"] = source.get("keyProperty", [])
            result["property_values"] = source.get("propertyValues", [])
            result["category_id"] = source.get("categoryId", "")
            result["sufficient_stock"] = source.get("storeQuantity", 0) > 0
            category_list = source.get("category", [])
            category_name = "没有找到类目"
            if (
                category_list
                and isinstance(category_list, list)
                and len(category_list) > 0
            ):
                category_name = category_list[-1].get(
                    "name", "没有找到类目"
                )  # 取最后一个类目的名字
            result["category"] = category_name
            result_list.append(result)
        return result_list  # 将 hits 替换为转换后的 result_list
    except requests.exceptions.RequestException as e:
        print(f"Elasticsearch搜索请求失败: {e}")
        return []


search_xianmu_product_directly_from_es("AT糖")
