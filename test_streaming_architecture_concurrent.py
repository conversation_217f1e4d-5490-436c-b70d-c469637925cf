#!/usr/bin/env python3
"""
测试新的流式更新架构 - 并发版本
验证预插入机制和实时持久化功能，支持并发测试和结构化输出
"""

import sys
import os
import time
import argparse
import re
import random
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.agent.api_query_processor import APIQueryProcessor
from src.services.feishu.query_processor import FeishuQueryProcessor
from src.services.chatbot.history_service import get_conversation_history_as_input_list, save_user_message
from src.utils.logger import logger

# 定义多个测试查询，覆盖所有agent
test_queries = [
    {
        "query": "杭州市昨天新增多少新注册门店？",
        "description": "测试sales_order_analytics - 新门店注册分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "为什么客户 TeaBaby茶宝宝 (ID: 559867) 不算拉新？",
        "description": "测试sales_order_analytics - 有效拉新判断",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "分析一下杭州市的客户昨天PB商品销售额",
        "description": "测试sales_order_analytics - PB商品销售额分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "查询大客户ID=1132155的门店，本月份的订单明细。列出大客户名字、门店名字和ID、门店地址、订单号、订单金额、商品名字、商品SKU、商品金额、商品件数、下单日",
        "description": "测试sales_order_analytics - 订单明细查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "查询大客户ID=1132155的门店，本月份的履约订单明细",
        "description": "测试sales_order_analytics - 履约订单明细查询",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "查询一下M1陈忠良的团队，昨天共售卖了多少流量品？按照商品名称和商品SKU分组，列出商品名字、商品SKU、商品金额、商品件数、下单日",
        "description": "测试sales_order_analytics - 销售额对比分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "帮我查一下SKU是 '三麟苏打汽水' 的商品最新的质检报告链接",
        "description": "测试warehouse_and_fulfillment - 质检报告查询",
        "expected_agent": "warehouse_and_fulfillment",
    },
    {
        "query": "对比一下'象牌苏打水', '三麟苏打汽水'在过去2个月的销售情况",
        "description": "测试sales_order_analytics - 销售额对比分析",
        "expected_agent": "sales_order_analytics",
    },
    {
        "query": "M1 李钱程团队本月的绩效分析",
        "description": "测试sales_kpi_analytics - 销售团队绩效分析",
        "expected_agent": "sales_kpi_analytics",
    },
    {
        "query": "安佳淡奶油在嘉兴仓的库存分析",
        "description": "测试warehouse_and_fulfillment - 库存分析",
        "expected_agent": "warehouse_and_fulfillment",
    },
    {
        "query": "你好，黑海盗纯牛奶什么时候有货，东莞仓",
        "description": "测试warehouse_and_fulfillment - 库存查询",
        "expected_agent": "warehouse_and_fulfillment",
    },
    {
        "query": "如何申请DMS账户",
        "description": "测试general_chat_bot - 知识问答",
        "expected_agent": "general_chat_bot",
    },
]

class TestResult:
    """测试结果数据结构"""
    def __init__(self, test_case: Dict[str, Any], test_id: int):
        self.test_case = test_case
        self.test_id = test_id
        self.start_time = None
        self.end_time = None
        self.duration = None
        self.actual_agent = None
        self.response_count = 0
        self.status = "pending"
        self.error_message = None
        self.responses = []
        self.conversation_id = None
        self.tool_usage = {}  # 记录每个tool的使用次数
        self.all_tools = []   # 记录所有使用过的tools

    def to_dict(self) -> Dict[str, Any]:
        return {
            "test_id": self.test_id,
            "query": self.test_case["query"],
            "description": self.test_case["description"],
            "expected_agent": self.test_case["expected_agent"],
            "actual_agent": self.actual_agent,
            "duration": self.duration,
            "response_count": self.response_count,
            "status": self.status,
            "error_message": self.error_message,
            "conversation_id": self.conversation_id,
            "responses": self.responses,
            "tool_usage": self.tool_usage,
            "all_tools": self.all_tools
        }

def extract_tool_names_from_content(content: str) -> List[str]:
    """
    从内容中提取toolname
    仅提取type='function_call'中的name字段
    """
    tool_names = []
    content_str = str(content)
    
    # 精确匹配：查找所有type='function_call'的ResponseFunctionToolCall中的name值
    # 示例：ResponseFunctionCall(...type='function_call'...name='fetch_ddl_for_table'...)
    pattern = r"ResponseFunction(?:ToolCall)?\([^)]*type=['\"]function_call['\"][^)]*?name=['\"]([^'\"]+)['\"]"
    matches = re.findall(pattern, content_str)
    
    # 添加所有匹配到的tool name
    for match in matches:
        if match and match != "function_call":
            tool_names.append(match)
    
    # 如果没有找到，尝试更简单的模式匹配
    if not tool_names:
        # 匹配所有name='xxx'，但只在包含function_call的上下文中
        func_pattern = r"name=['\"]([^'\"]+)['\"][^)]*type=['\"]function_call['\"]"
        func_matches = re.findall(func_pattern, content_str)
        tool_names.extend([m for m in func_matches if m and m != "function_call"])
    
    return list(set(tool_names))  # 去重

def run_single_test(test_result: TestResult, user_info: Dict[str, str]) -> TestResult:
    """运行单个测试"""
    processor = APIQueryProcessor()
    test_result.start_time = time.time()
    test_result.conversation_id = f"test_conv_{int(time.time())}_{test_result.test_id}"
    
    try:
        # 保存用户查询到chat_history
        save_success = save_user_message(
            username=user_info["name"],
            email=user_info["email"],
            conversation_id=test_result.conversation_id,
            content=test_result.test_case["query"]
        )
        
        if not save_success:
            test_result.status = "failed"
            test_result.error_message = "Failed to save user query"
            return test_result

        # 执行流式查询
        response_count = 0
        agent_detected = False
        
        for response in processor.run_query(
            user_query=test_result.test_case["query"],
            user_info=user_info,
            conversation_id=test_result.conversation_id,
        ):
            response_count += 1
            test_result.responses.append(response)
            
            # 检测实际调用的agent
            if test_result.test_case["expected_agent"] in response and not agent_detected:
                test_result.actual_agent = test_result.test_case["expected_agent"]
                agent_detected = True

        test_result.response_count = response_count
        
        # 验证数据库中的记录
        history = get_conversation_history_as_input_list(
            user_info["name"], user_info["email"], test_result.conversation_id
        )
        
        # 提取tool usage信息
        try:
            from src.services.chatbot.history_service import get_conversation_messages
            messages = get_conversation_messages(
                conversation_id=test_result.conversation_id,
                username=user_info["name"],
                email=user_info["email"]
            )
            for msg in messages:
                if msg.get("role") == "assistant":
                    content = msg.get("content", "")
                    logs = str(msg.get("logs", ""))
                    
                    # 从内容和日志中提取toolname
                    tools_in_content = extract_tool_names_from_content(content)
                    tools_in_logs = extract_tool_names_from_content(logs)
                    all_tools = tools_in_content + tools_in_logs
                    
                    for tool_name in all_tools:
                        if tool_name not in test_result.tool_usage:
                            test_result.tool_usage[tool_name] = 0
                        test_result.tool_usage[tool_name] += 1
                        
                        if tool_name not in test_result.all_tools:
                            test_result.all_tools.append(tool_name)
        except Exception as e:
            logger.warning(f"提取tool usage失败: {str(e)}")
        
        if response_count > 0 and len(history) > 0:
            test_result.status = "passed"
        else:
            test_result.status = "failed"
            test_result.error_message = "No responses or history records"
            
    except Exception as e:
        test_result.status = "failed"
        test_result.error_message = str(e)
        logger.exception(f"测试查询 {test_result.test_id} 异常")
    
    test_result.end_time = time.time()
    test_result.duration = test_result.end_time - test_result.start_time
    
    return test_result

def run_concurrent_tests(queries: List[Dict[str, Any]], max_workers: int = 3) -> List[TestResult]:
    """运行并发测试"""
    today = datetime.now().strftime('%Y%m%d')
    user_info = {
        "name": "ChatBI自动化测试",
        "email": f"test_user_{today}@summerfarm.net",
        "open_id": "test_open_id",
    }
    
    results = []
    
    # 创建测试任务
    test_tasks = []
    for i, query in enumerate(queries, 1):
        test_result = TestResult(query, i)
        test_tasks.append((test_result, user_info))
    
    # 使用线程池执行并发测试
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_result = {
            executor.submit(run_single_test, task[0], task[1]): task[0] 
            for task in test_tasks
        }
        
        for future in as_completed(future_to_result):
            test_result = future.result()
            results.append(test_result)
            print(f"✓ 测试 {test_result.test_id} 完成，耗时 {test_result.duration:.2f}s")
    
    return sorted(results, key=lambda x: x.test_id)

def generate_markdown_report(results: List[TestResult], total_time: float, content_length: int = 2000, concurrent_users: int = 3) -> str:
    """生成markdown格式的测试报告"""
    today = datetime.now().strftime('%Y%m%d')
    
    # 创建测试用户信息
    user_info = {
        "name": "ChatBI自动化测试",
        "email": f"test_user_{today}@summerfarm.net",
        "open_id": "test_open_id",
    }
    
    # 计算统计信息
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r.status == "passed")
    failed_tests = total_tests - passed_tests
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # 统计SQL错误（包括Unknown column和其他SQL执行错误）
    sql_error_count = 0
    sql_error_details = []
    
    for result in results:
        error_count = 0
        error_details_list = []
        
        # 方法1：检查错误消息中的SQL错误
        if result.error_message:
            unknown_column_matches = len(re.findall(r'Unknown column', result.error_message))
            sql_error_matches = len(re.findall(r'Error executing SQL', result.error_message))
            error_count += unknown_column_matches + sql_error_matches
            
            if unknown_column_matches > 0 or sql_error_matches > 0:
                error_details_list.append(result.error_message)
        
        # 方法2：检查响应中的SQL错误
        for response in result.responses:
            response_str = str(response)
            unknown_column_matches = len(re.findall(r'Unknown column', response_str))
            sql_error_matches = len(re.findall(r'Error executing SQL', response_str))
            error_count += unknown_column_matches + sql_error_matches
            
            if unknown_column_matches > 0 or sql_error_matches > 0:
                error_details_list.append(response_str)
        
        # 方法3：从数据库日志中检测SQL错误
        try:
            from src.services.chatbot.history_service import get_conversation_messages
            
            messages = get_conversation_messages(
                conversation_id=result.conversation_id,
                username=user_info["name"],
                email=user_info["email"]
            )
            
            for msg in messages:
                if msg.get("role") == "assistant":
                    logs = str(msg.get("logs", ""))
                    
                    # 统计"执行失败:"的出现次数
                    execution_failure_matches = re.findall(r'执行失败:', logs)
                    fetch_sql_matches = re.findall(r'Called tool:fetch_mysql_sql_result with args', logs)
                    
                    # 只有在同时出现fetch_mysql_sql_result和"执行失败:"时才计数
                    if fetch_sql_matches and execution_failure_matches:
                        error_count += len(execution_failure_matches)
                        
                        # 提取具体的错误详情
                        error_pattern = r'(执行失败:.*?Error executing SQL[^\n]*)'
                        specific_errors = re.findall(error_pattern, logs, re.DOTALL)
                        error_details_list.extend(specific_errors)
                    
                    # 统计其他SQL错误
                    unknown_column_matches = len(re.findall(r'Unknown column', logs))
                    sql_syntax_matches = len(re.findall(r'SQL syntax', logs))
                    table_not_exist_matches = len(re.findall(r'Table.*doesn\'t exist', logs))
                    
                    additional_errors = unknown_column_matches + sql_syntax_matches + table_not_exist_matches
                    if additional_errors > 0:
                        error_count += additional_errors
                        error_details_list.append(logs)
                        
        except Exception as e:
            logger.warning(f"检测SQL错误时失败: {str(e)}")
        
        sql_error_count += error_count
        if error_count > 0:
            sql_error_details.append({
                "test_id": result.test_id,
                "query": result.test_case["query"],
                "error_count": error_count,
                "errors": error_details_list
            })
    
    # 按agent分组统计
    agent_stats = {}
    total_tool_usage = {}  # 全局tool usage统计
    
    for result in results:
        agent = result.test_case["expected_agent"]
        if agent not in agent_stats:
            agent_stats[agent] = {
                "total": 0, "passed": 0, "failed": 0, "avg_duration": 0, 
                "sql_errors": 0, "tool_usage": {}, "tools_used": []
            }
        agent_stats[agent]["total"] += 1
        agent_stats[agent]["passed"] += 1 if result.status == "passed" else 0
        agent_stats[agent]["failed"] += 1 if result.status == "failed" else 0
        agent_stats[agent]["avg_duration"] += result.duration
        
        # 合并tool usage
        for tool_name, count in result.tool_usage.items():
            if tool_name not in agent_stats[agent]["tool_usage"]:
                agent_stats[agent]["tool_usage"][tool_name] = 0
            agent_stats[agent]["tool_usage"][tool_name] += count
            
            if tool_name not in agent_stats[agent]["tools_used"]:
                agent_stats[agent]["tools_used"].append(tool_name)
                
            # 更新全局统计
            if tool_name not in total_tool_usage:
                total_tool_usage[tool_name] = 0
            total_tool_usage[tool_name] += count
        
        # 统计该agent的SQL错误总数
        agent_sql_errors = 0
        for error_detail in sql_error_details:
            if error_detail["test_id"] == result.test_id:
                agent_sql_errors += error_detail["error_count"]
                break
        
        agent_stats[agent]["sql_errors"] += agent_sql_errors
    
    for agent in agent_stats:
        if agent_stats[agent]["total"] > 0:
            agent_stats[agent]["avg_duration"] /= agent_stats[agent]["total"]
    
    # 生成报告
    report = f"""# 流式架构测试报告 - {today}

## 测试概要

- **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **测试用例总数**: {total_tests}
- **通过测试**: {passed_tests}
- **失败测试**: {failed_tests}
- **通过率**: {pass_rate:.1f}%
- **SQL错误总数**: {sql_error_count}
- **总耗时**: {total_time:.2f}秒
- **并发数**: {concurrent_users}

## SQL错误详情
"""

    if sql_error_details:
        report += f"检测到 **{sql_error_count}** 个SQL错误:\n\n"
        for error in sql_error_details:
            report += f"- **测试 {error['test_id']}**: {error['query'][:100]}{'...' if len(error['query']) > 100 else ''}\n"
            report += f"  - **错误次数**: {error['error_count']} 次\n"
            if error['errors']:
                for i, err in enumerate(error['errors'][:3], 1):  # 最多显示3个错误
                    error_summary = str(err)[:150] + '...' if len(str(err)) > 150 else str(err)
                    report += f"    {i}. {error_summary}\n"
                if len(error['errors']) > 3:
                    report += f"    ... 还有 {len(error['errors']) - 3} 个错误\n"
            report += "\n"
    else:
        report += "✅ 未检测到SQL错误\n\n"

    report += """## Tool Usage 统计

### 全局Tool使用统计
"""
    
    if total_tool_usage:
        for tool_name, count in sorted(total_tool_usage.items(), key=lambda x: x[1], reverse=True):
            report += f"- **{tool_name}**: {count} 次\n"
    else:
        report += "- 未检测到tool调用\n"
    
    report += "\n## Agent统计\n\n"
    
    for agent, stats in agent_stats.items():
        agent_pass_rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
        report += f"""
### {agent}
- **测试数量**: {stats["total"]}
- **通过数量**: {stats["passed"]}
- **失败数量**: {stats["failed"]}
- **通过率**: {agent_pass_rate:.1f}%
- **SQL错误**: {stats["sql_errors"]}
- **平均耗时**: {stats["avg_duration"]:.2f}秒
- **使用Tools**: {', '.join(stats["tools_used"]) if stats["tools_used"] else '无'}
"""
        
        if stats["tool_usage"]:
            report += "- **Tool使用详情**:\n"
            for tool_name, count in sorted(stats["tool_usage"].items(), key=lambda x: x[1], reverse=True):
                report += f"  - {tool_name}: {count} 次\n"
        
        report += "\n"
    
    report += "## 详细测试结果\n\n"
    
    for result in results:
        status_icon = "✅" if result.status == "passed" else "❌"
        report += f"""
### {status_icon} 测试 {result.test_id}: {result.test_case['description']}

**查询内容**:
```
{result.test_case['query']}
```

**预期Agent**: `{result.test_case['expected_agent']}`
**实际Agent**: `{result.actual_agent or '未检测到'}`
**状态**: `{result.status}`
**耗时**: {result.duration:.2f}秒
**响应数量**: {result.response_count}
**会话ID**: `{result.conversation_id}`

"""
        
        if result.tool_usage:
            report += "**Tool使用情况**:\n"
            for tool_name, count in sorted(result.tool_usage.items(), key=lambda x: x[1], reverse=True):
                report += f"- {tool_name}: {count} 次\n"
            report += "\n"
        
        if result.all_tools:
            report += f"**使用Tools**: {', '.join(result.all_tools)}\n\n"
        
        if result.error_message:
            report += f"**错误信息**: {result.error_message}\n\n"
        
        # 从数据库获取完整的聊天内容
        try:
            from src.services.chatbot.history_service import get_conversation_messages
            
            messages = get_conversation_messages(
                conversation_id=result.conversation_id,
                username=user_info["name"],
                email=user_info["email"]
            )
            
            if messages:
                # 找到最后一条助手的回复
                assistant_messages = [msg for msg in messages if msg.get("role") == "assistant"]
                if not assistant_messages:
                    report += "**最终测试结果**: 未找到助手的回复\n\n"
                    
                # 显示完整的对话记录
                report += "**完整对话记录**:\n"
                for i, msg in enumerate(messages, 1):
                    role = "用户" if msg.get("role") == "user" else "助手"
                    content = msg.get("content", "")
                    logs = msg.get("logs")
                    
                    # 检测SQL错误
                    if role == "助手" and logs:
                        logs_str = str(logs)
                        if "Called tool:fetch_mysql_sql_result with args" in logs_str and "执行失败:" in logs_str:
                            report += "**⚠️SQL错误**: 检测到SQL执行失败\n\n"
                        elif "Unknown column" in logs_str:
                            report += "**⚠️SQL错误**: Unknown column错误\n\n"
                        elif "Error executing SQL" in logs_str:
                            report += "**⚠️SQL错误**: SQL执行错误\n\n"
                    
                    report += f"**{i}. {role}**: {content[:content_length]}{'...' if len(content) > content_length else ''}\n\n"
            else:
                report += "**测试结果**: 无法从数据库获取对话记录\n"
        except Exception as e:
            logger.exception(f"获取{result.conversation_id}的测试结果失败: {str(e)}")
            report += f"**获取测试结果失败**: {str(e)}\n"
        
        report += "\n---\n\n"
    
    return report

def save_markdown_report(report: str, filename: str = None):
    """保存markdown报告到文件"""
    if filename is None:
        today = datetime.now().strftime('%Y%m%d')
        filename = f"test_streaming_result_{today}.md"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ 测试报告已保存到: {filename}")
    return filename

def test_streaming_threshold():
    """测试流式更新阈值设置"""
    print("=" * 60)
    print("测试流式更新阈值设置")
    print("=" * 60)

    from src.services.agent.base_query_processor import STREAMING_UPDATE_THRESHOLD

    print(f"当前流式更新阈值: {STREAMING_UPDATE_THRESHOLD} 字符")

    if STREAMING_UPDATE_THRESHOLD == 15:
        print("✓ 阈值已正确设置为15字符")
        return True
    else:
        print(f"✗ 阈值应该是15字符，但当前是{STREAMING_UPDATE_THRESHOLD}字符")
        return False

async def test_feishu_streaming():
    """测试飞书流式更新架构"""
    print("=" * 60)
    print("测试飞书流式更新架构")
    print("=" * 60)

    try:
        processor = FeishuQueryProcessor()
        print("飞书处理器创建成功")
        print("注意：完整的飞书测试需要真实的飞书环境")
        return True
    except Exception as e:
        print(f"飞书测试失败: {e}")
        logger.exception("飞书测试异常")
        return False

def main():
    """主测试函数 - 并发版本"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='并发测试流式更新架构')
    parser.add_argument('--cu', type=int, default=3, 
                       help='并发数 (concurrent users), 默认为3')
    parser.add_argument('--content-length', type=int, default=2000,
                       help='内容截取长度，默认为2000字符')
    parser.add_argument('--n', type=int, default=None,
                       help='指定测试用例数量，默认为全部，如果指定则随机选择')
    args = parser.parse_args()
    
    concurrent_users = args.cu
    content_length = args.content_length
    test_case_count = args.n
    
    # 根据参数选择测试用例
    if test_case_count is not None and test_case_count > 0:
        # 随机选择指定数量的测试用例
        selected_queries = random.sample(test_queries, min(test_case_count, len(test_queries)))
        print(f"随机选择 {len(selected_queries)} 个测试用例")
    else:
        # 使用全部测试用例
        selected_queries = test_queries
        print(f"使用全部 {len(test_queries)} 个测试用例")
    
    print("开始并发测试新的流式更新架构")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"实际测试用例数: {len(selected_queries)}")
    print(f"并发数: {concurrent_users}")
    print(f"内容截取长度: {content_length}字符")
    
    start_time = time.time()
    
    # 运行并发测试
    print("\n" + "=" * 80)
    print("开始并发测试...")
    print("=" * 80)
    
    results = run_concurrent_tests(selected_queries, max_workers=concurrent_users)
    
    total_time = time.time() - start_time
    
    # 打印简要结果
    passed_tests = sum(1 for r in results if r.status == "passed")
    total_tests = len(results)
    pass_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    # 计算SQL错误数（按实际出现次数统计）
    sql_error_count = 0
    for result in results:
        error_count = 0
        
        # 方法1：检查错误消息中的SQL错误
        if result.error_message:
            unknown_column_matches = len(re.findall(r'Unknown column', result.error_message))
            sql_error_matches = len(re.findall(r'Error executing SQL', result.error_message))
            error_count += unknown_column_matches + sql_error_matches
        
        # 方法2：检查响应中的SQL错误
        for response in result.responses:
            response_str = str(response)
            unknown_column_matches = len(re.findall(r'Unknown column', response_str))
            sql_error_matches = len(re.findall(r'Error executing SQL', response_str))
            error_count += unknown_column_matches + sql_error_matches
        
        # 方法3：从数据库日志中检测SQL错误
        try:
            from src.services.chatbot.history_service import get_conversation_messages
            
            messages = get_conversation_messages(
                conversation_id=result.conversation_id,
                username="ChatBI自动化测试",
                email=f"test_user_{datetime.now().strftime('%Y%m%d')}@summerfarm.net"
            )
            
            for msg in messages:
                if msg.get("role") == "assistant":
                    logs = str(msg.get("logs", ""))
                    
                    # 统计"执行失败:"的出现次数
                    execution_failure_matches = re.findall(r'执行失败:', logs)
                    fetch_sql_matches = re.findall(r'Called tool:fetch_mysql_sql_result with args', logs)
                    
                    # 只有在同时出现fetch_mysql_sql_result和"执行失败:"时才计数
                    if fetch_sql_matches and execution_failure_matches:
                        error_count += len(execution_failure_matches)
                    
                    # 统计其他SQL错误
                    unknown_column_matches = len(re.findall(r'Unknown column', logs))
                    sql_syntax_matches = len(re.findall(r'SQL syntax', logs))
                    table_not_exist_matches = len(re.findall(r'Table.*doesn\'t exist', logs))
                    
                    error_count += unknown_column_matches + sql_syntax_matches + table_not_exist_matches
                        
        except Exception as e:
            logger.warning(f"检测SQL错误时失败: {str(e)}")
        
        sql_error_count += error_count
    
    # 计算全局tool usage
    global_tool_usage = {}
    for result in results:
        for tool_name, count in result.tool_usage.items():
            if tool_name not in global_tool_usage:
                global_tool_usage[tool_name] = 0
            global_tool_usage[tool_name] += count
    
    # 生成并保存报告
    report = generate_markdown_report(results, total_time, content_length, concurrent_users)
    filename = save_markdown_report(report)
    
    print("\n" + "=" * 80)
    print("并发测试完成")
    print("=" * 80)
    print(f"总测试用例: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {pass_rate:.1f}%")
    print(f"SQL错误数: {sql_error_count}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"并发数: {concurrent_users}")
    print(f"报告文件: {filename}")
    print(f"内容截取长度: {content_length}字符")
    
    if global_tool_usage:
        print("\nTool使用统计:")
        for tool_name, count in sorted(global_tool_usage.items(), key=lambda x: x[1], reverse=True):
            print(f"  {tool_name}: {count} 次")
    else:
        print("\n未检测到tool调用")
    
    if passed_tests == total_tests:
        print("🎉 所有并发测试都通过了！")
        return 0
    else:
        print("⚠️  部分测试失败，请查看详细报告")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)