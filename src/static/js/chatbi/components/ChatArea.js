import { computed } from 'vue';
import UserMessage from './UserMessage.js';
import AiMessage from './AiMessage.js';


export default {
    name: 'Chat<PERSON><PERSON>',
    components: {
        UserMessage,
        AiMessage
    },
    emits: ['message-marked-as-bad-case', 'message-marked-as-good-case', 'share-conversation'],
    props: {
        messages: {
            type: Array,
            default: () => []
        },
        isLoading: {
            type: Boolean,
            default: false
        },
        error: {
            type: String,
            default: null
        },
        conversationId: {
            type: String,
            default: ''
        },
        showToastNotifications: {
            type: Boolean,
            default: true
        },
        allowUnmarkBadCase: {
            type: Boolean,
            default: false
        },
        allowUnmarkGoodCase: {
            type: Boolean,
            default: true
        }
    },
    setup(props) {
        return {};
    },
    template: `
        <div class="w-full flex-1 flex flex-col items-center relative">
            <!-- 错误状态 -->
            <!-- 错误状态 -->
            <div v-if="error" class="w-full flex items-center justify-center p-2 sm:p-4 md:p-6">
                <div class="text-center p-4 sm:p-8 max-w-md">
                    <div class="text-error mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">加载失败</h3>
                    <p class="text-base-content/70">{{ error }}</p>
                </div>
            </div>

            <!-- 消息列表 -->
            <div v-if="messages && messages.length > 0" class="w-full space-y-6">
                <div class="space-y-6 sm:space-y-8 w-full min-w-0">
                    <div v-for="message in messages" :key="message.id" class="w-full min-w-0">
                        <UserMessage
                            v-if="message.role === 'user'"
                            :id="message.id"
                            :content="message.content"
                            :images="message.images || []"
                            :timestamp="message.timestamp"
                        />
                        <AiMessage
                            v-else-if="message.role === 'assistant'"
                            :content="message.content"
                            :rendered-content="message.renderedContent"
                            :timestamp="message.timestamp"
                            :time-spend="message.time_spend"
                            :is-streaming="message.isStreaming"
                            :is-error="message.isError"
                            :is-interrupted="message.isInterrupted"
                            :conversation-id="conversationId"
                            :is-bad-case="message.isBadCase"
                            :is-good-case="message.isGoodCase"
                            :showToastNotifications="showToastNotifications"
                            :allowUnmarkBadCase="allowUnmarkBadCase"
                            :allowUnmarkGoodCase="allowUnmarkGoodCase"
                            @marked-as-bad-case="$emit('message-marked-as-bad-case', $event)"
                            @marked-as-good-case="$emit('message-marked-as-good-case', $event)"
                            @share-conversation="$emit('share-conversation', $event)"
                        />
                    </div>
                </div>
            </div>
        </div>
    `
};
