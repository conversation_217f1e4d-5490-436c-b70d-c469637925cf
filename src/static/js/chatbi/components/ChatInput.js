import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { SendIcon, SquareIcon } from '../../utils/Icons.js';
import { sendQuery } from '../services/queryService.js';
import QuestionPrompts from './QuestionPrompts.js';
import {
    uploadImage,
    extractImageFromPaste,
    createImagePreviewUrl,
    revokeImagePreviewUrl,
    validateImageFile,
    formatFileSize
} from '../services/uploadService.js';

export default {
    name: 'ChatInput',
    components: {
        QuestionPrompts
    },
    props: {
        conversationId: {
            type: String,
            default: null
        },
        isDevLogVisible: {
            type: Boolean,
            default: false
        },
        messages: {
            type: Array,
            default: () => []
        }
    },
    emits: ['message-sent', 'message-start', 'message-stream', 'message-complete', 'message-error', 'message-interrupting', 'toggle-dev-log'],
    setup(props, { emit }) {
        const message = ref('');
        const isLoading = ref(false);
        const isStreaming = ref(false);
        const textareaRef = ref(null);
        const queryController = ref(null);
        const isHistoryTransition = ref(false);

        // 图片上传相关状态
        const uploadedImages = ref([]);
        const isUploading = ref(false);
        const uploadProgress = ref(0);

        // 计算是否显示问题列表（只有输入框为空时才显示）
        const showQuestionPrompts = computed(() => {
            return !props.conversationId &&
                   props.messages.length === 0 &&
                   message.value.trim() === '';
        });

        // 监听conversationId变化，检测是否是从新对话切换到历史对话
        watch(() => props.conversationId, (newId, oldId) => {
            // 如果从null（新对话）切换到非null（历史对话），标记为历史转换
            if (oldId === null && newId !== null) {
                isHistoryTransition.value = true;
                // 短暂延迟后重置标记，确保动画有时间执行
                setTimeout(() => {
                    isHistoryTransition.value = false;
                }, 100);
            }
        });

        // 根据内容自动调整高度
        const adjustTextareaHeight = () => {
            if (!textareaRef.value) return;

            // 重置高度以获取正确的scrollHeight
            textareaRef.value.style.height = 'auto';

            // 设置新高度，但限制最大高度
            const newHeight = Math.min(Math.max(textareaRef.value.scrollHeight, 24), 200);
            textareaRef.value.style.height = `${newHeight}px`;
        };

        // 计算输入框的行数
        const textareaRows = computed(() => {
            const lineCount = (message.value.match(/\n/g) || []).length + 1;
            return Math.min(lineCount, 8); // 最多显示8行
        });

        // 中断当前查询
        const interruptQuery = () => {
            if (queryController.value) {
                // 显示中断状态
                emit('message-interrupting');

                // 中断请求
                queryController.value.abort();

                // 重置状态，但不清除消息
                // 注意：不再发送message-error，因为这会导致消息被清除
                // 中断处理现在在queryService中完成，并通过onMessageComplete回调通知
                queryController.value = null;
                isStreaming.value = false;
                isLoading.value = false;
            }
        };

        // 发送消息
        const sendMessage = async () => {
            // 如果正在流式传输，则中断当前查询
            if (isStreaming.value) {
                interruptQuery();
                return;
            }

            // 检查消息是否为空或正在加载
            if (!message.value.trim() && uploadedImages.value.length === 0) return;
            if (isLoading.value || isUploading.value) return;

            // 检查是否有上传失败的图片
            const failedImages = uploadedImages.value.filter(img => img.error);
            if (failedImages.length > 0) {
                alert('请先处理上传失败的图片');
                return;
            }

            // 检查是否有正在上传的图片
            const uploadingImages = uploadedImages.value.filter(img => img.uploading);
            if (uploadingImages.length > 0) {
                alert('请等待图片上传完成');
                return;
            }

            try {
                isLoading.value = true;
                const userMessage = message.value.trim();

                // 收集成功上传的图片URL
                const imageUrls = uploadedImages.value
                    .filter(img => img.url && !img.error && !img.uploading)
                    .map(img => img.url);

                // 清空输入框和图片列表
                message.value = '';
                const imagesToCleanup = [...uploadedImages.value];
                uploadedImages.value = [];

                // 释放预览URL
                imagesToCleanup.forEach(img => {
                    if (img.previewUrl) {
                        revokeImagePreviewUrl(img.previewUrl);
                    }
                });

                if (textareaRef.value) {
                    textareaRef.value.style.height = '24px';
                }

                // 通知父组件用户消息已发送
                emit('message-sent', {
                    content: userMessage,
                    images: imageUrls,
                    timestamp: Date.now()
                });

                // 通知父组件开始AI回复
                emit('message-start');

                // 开始流式查询
                isStreaming.value = true;
                queryController.value = sendQuery({
                    query: userMessage,
                    images: imageUrls,
                    conversationId: props.conversationId,
                    onMessageStart: (state) => {
                        console.log('开始接收AI回复', state);
                    },
                    onMessageUpdate: (chunk, state) => {
                        // 通知父组件消息流更新
                        const streamData = {
                            chunk,
                            fullMessage: state.rawContent,
                            renderedContent: state.renderedContent,
                            state
                        };
                        emit('message-stream', streamData);
                    },
                    onMessageComplete: (state) => {
                        isStreaming.value = false;
                        isLoading.value = false;
                        queryController.value = null;

                        // 通知父组件消息完成
                        const completeData = {
                            content: state.rawContent,
                            renderedContent: state.renderedContent,
                            conversationId: state.conversationId || props.conversationId,
                            logs: state.logsHTML,
                            timestamp: Date.now(),
                            isInterrupted: state.isInterrupted || false // 添加中断状态标记
                        };
                        emit('message-complete', completeData);
                    },
                    onError: (errorMessage, state) => {
                        console.error('查询错误:', errorMessage, state);
                        isStreaming.value = false;
                        isLoading.value = false;
                        queryController.value = null;

                        // 通知父组件发生错误
                        emit('message-error', {
                            message: errorMessage,
                            state
                        });
                    }
                });

            } catch (error) {
                console.error('发送消息失败:', error);
                isStreaming.value = false;
                isLoading.value = false;
                queryController.value = null;
                emit('message-error', error.message || '发送消息失败');
            }
        };

        // 处理按键事件
        const handleKeydown = (event) => {
            // 按下Enter键发送消息，按下Shift+Enter换行
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        };

        // 处理输入事件，调整高度
        const handleInput = () => {
            adjustTextareaHeight();
        };

        // 处理图片粘贴
        const handlePaste = async (event) => {
            console.log('粘贴事件触发', event);

            try {
                const imageFile = extractImageFromPaste(event);
                console.log('提取的图片文件:', imageFile);

                if (imageFile) {
                    console.log('检测到图片文件，开始上传:', imageFile.name, imageFile.type, imageFile.size);
                    event.preventDefault();
                    await handleImageUpload(imageFile);
                } else {
                    console.log('未检测到图片文件');
                }
            } catch (error) {
                console.error('处理粘贴事件时出错:', error);
            }
        };

        // 处理图片上传
        const handleImageUpload = async (file) => {
            console.log('开始处理图片上传:', file);

            // 验证图片文件
            const validation = validateImageFile(file);
            console.log('图片验证结果:', validation);

            if (!validation.valid) {
                console.error('图片验证失败:', validation.error);
                alert(validation.error);
                return;
            }

            try {
                isUploading.value = true;
                uploadProgress.value = 0;

                // 创建预览URL
                const previewUrl = createImagePreviewUrl(file);

                // 添加到上传列表（显示预览）
                const imageItem = {
                    id: Date.now(),
                    file,
                    previewUrl,
                    uploading: true,
                    progress: 0,
                    url: null,
                    error: null
                };
                uploadedImages.value.push(imageItem);

                // 上传图片
                const result = await uploadImage(file, (progress) => {
                    imageItem.progress = progress;
                    uploadProgress.value = progress;
                });

                // 上传成功
                imageItem.uploading = false;
                imageItem.url = result.url;
                imageItem.filename = result.filename;
                imageItem.size = result.size;

                console.log('图片上传成功:', result);

            } catch (error) {
                console.error('图片上传失败:', error);

                // 更新错误状态
                const imageItem = uploadedImages.value[uploadedImages.value.length - 1];
                if (imageItem) {
                    imageItem.uploading = false;
                    imageItem.error = error.message;
                }

                alert(`图片上传失败: ${error.message}`);
            } finally {
                isUploading.value = false;
                uploadProgress.value = 0;
            }
        };

        // 移除已上传的图片
        const removeImage = (imageId) => {
            const index = uploadedImages.value.findIndex(img => img.id === imageId);
            if (index !== -1) {
                const image = uploadedImages.value[index];
                // 释放预览URL
                if (image.previewUrl) {
                    revokeImagePreviewUrl(image.previewUrl);
                }
                uploadedImages.value.splice(index, 1);
            }
        };

        // 打开图片预览模态框
        const openImagePreview = (imageUrl, imageName) => {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 cursor-pointer';
            modal.onclick = () => document.body.removeChild(modal);

            // 创建图片容器
            const container = document.createElement('div');
            container.className = 'relative max-w-[90vw] max-h-[90vh] bg-white rounded-lg overflow-hidden';
            container.onclick = (e) => e.stopPropagation();

            // 创建图片
            const img = document.createElement('img');
            img.src = imageUrl;
            img.className = 'max-w-full max-h-full object-contain';
            img.alt = imageName || '预览图片';

            // 创建关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '✕';
            closeBtn.className = 'absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors';
            closeBtn.onclick = () => document.body.removeChild(modal);

            // 创建图片名称标签
            if (imageName) {
                const nameLabel = document.createElement('div');
                nameLabel.textContent = imageName;
                nameLabel.className = 'absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 text-sm';
                container.appendChild(nameLabel);
            }

            container.appendChild(img);
            container.appendChild(closeBtn);
            modal.appendChild(container);
            document.body.appendChild(modal);
        };

        // 处理问题点击
        const handleQuestionClick = (question) => {
            // 设置消息内容为问题文本
            message.value = question.text;

            // 调整输入框高度
            setTimeout(() => {
                adjustTextareaHeight();
            }, 0);

            // 注意：不自动发送，只是填充输入框
        };

        // 组件挂载后初始化
        onMounted(() => {
            if (textareaRef.value) {
                textareaRef.value.style.height = '24px';
                console.log('ChatInput组件已挂载，图片粘贴功能已启用');
                console.log('textarea元素:', textareaRef.value);
                console.log('textarea事件监听器:', textareaRef.value.onpaste);
            } else {
                console.warn('ChatInput: textareaRef.value 为空');
            }
        });

        return {
            message,
            isLoading,
            isStreaming,
            textareaRef,
            textareaRows,
            sendMessage,
            interruptQuery,
            handleKeydown,
            handleInput,
            showQuestionPrompts,
            handleQuestionClick,
            isHistoryTransition,
            SendIcon,
            SquareIcon,
            // 图片上传相关
            uploadedImages,
            isUploading,
            uploadProgress,
            handlePaste,
            handleImageUpload,
            removeImage,
            openImagePreview,
            formatFileSize
        };
    },
    template: `
        <div class="w-full px-4 md:px-10 min-w-0 chat-input-container">
            <!-- 主输入区域 -->
            <div class="flex flex-col w-full border min-w-0 chat-input-box relative">
                <!-- 问题提示区域 - positioned absolutely to not affect layout flow -->
                <QuestionPrompts
                    :show="showQuestionPrompts"
                    :is-history-transition="isHistoryTransition"
                    @question-click="handleQuestionClick"
                    class="absolute bottom-full left-0 right-0 mb-2 z-10 w-full"
                />

                <!-- 图片预览区域 -->
                <div v-if="uploadedImages.length > 0" class="px-3 pt-3 pb-2">
                    <div class="flex flex-wrap gap-2">
                        <div
                            v-for="image in uploadedImages"
                            :key="image.id"
                            class="relative group"
                        >
                            <!-- 图片预览 -->
                            <div class="w-16 h-16 rounded-lg overflow-hidden border border-base-300 bg-base-200 relative cursor-pointer hover:opacity-80 transition-opacity">
                                <img
                                    v-if="image.previewUrl"
                                    :src="image.previewUrl"
                                    :alt="image.file?.name || 'Uploaded image'"
                                    class="w-full h-full object-cover"
                                    @click="openImagePreview(image.previewUrl, image.file?.name)"
                                    title="点击查看大图"
                                />

                                <!-- 上传进度遮罩 -->
                                <div
                                    v-if="image.uploading"
                                    class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center"
                                >
                                    <div class="text-white text-xs">{{ Math.round(image.progress) }}%</div>
                                </div>

                                <!-- 错误遮罩 -->
                                <div
                                    v-if="image.error"
                                    class="absolute inset-0 bg-red-500 bg-opacity-70 flex items-center justify-center"
                                    :title="image.error"
                                >
                                    <div class="text-white text-xs">✕</div>
                                </div>

                                <!-- 删除按钮 -->
                                <button
                                    v-if="!image.uploading"
                                    @click="removeImage(image.id)"
                                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                    title="删除图片"
                                >
                                    ✕
                                </button>
                            </div>

                            <!-- 图片信息 -->
                            <div class="text-xs text-base-content/60 mt-1 max-w-16 truncate">
                                {{ image.file?.name || 'image' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="w-full px-3 pt-3 pb-1 chat-input-area">
                    <textarea
                        ref="textareaRef"
                        v-model="message"
                        class="w-full resize-none focus:outline-none border-0 bg-transparent min-w-0 p-0 text-[16px] leading-6 min-h-[24px] max-h-[200px] chat-input-textarea scrollbar-auto"
                        placeholder="输入您的问题或粘贴图片..."
                        :rows="textareaRows"
                        @keydown="handleKeydown"
                        @input="handleInput"
                        @paste="handlePaste"
                        style="height: 24px; background-color: transparent;"
                    ></textarea>
                </div>

                <!-- 功能按钮区域 -->
                <div class="flex items-center justify-between py-1.5 px-3 chat-input-footer">
                    <span class="text-xs font-light tracking-wide chat-input-footer-text">
                        Developed by XM, Powered by Gemini
                    </span>
                    <div class="flex items-center">
                        <!-- 发送按钮 -->
                        <button
                            :class="[
                                'p-2.5 rounded-full transition-all duration-200 disabled:cursor-not-allowed chat-input-send-button',
                                isStreaming || ((message.trim() || uploadedImages.length > 0) && !isLoading && !isUploading) ? 'chat-input-send-button-active' : 'chat-input-send-button-inactive'
                            ]"
                            :disabled="(!message.trim() && uploadedImages.length === 0 && !isStreaming) || (isLoading && !isStreaming) || isUploading"
                            @click="sendMessage"
                            :title="isStreaming ? '中断查询' : isUploading ? '正在上传图片...' : '发送'"
                        >
                            <span v-if="isLoading && !isStreaming" class="loading loading-spinner loading-sm"></span>
                            <span v-else-if="isStreaming" class="w-5 h-5 chat-input-interrupt-icon" v-html="SquareIcon"></span>
                            <span v-else class="w-5 h-5 chat-input-send-icon" v-html="SendIcon"></span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="text-xs text-base-content/50 mt-2 text-center chat-input-hint">
                ChatBI 的回答未必准确无误，请仔细核查数据
            </div>
        </div>
    `
};
