/**
 * Chat Content Component
 *
 * 聊天内容区域组件，包含消息区域和输入区域
 * 在桌面和移动视图中共享使用
 */
import ChatArea from './ChatArea.js';
import ChatInput from './ChatInput.js';
import DevLogPanel from './DevLogPanel.js';

export default {
    name: 'ChatContent',
    components: {
        ChatArea,
        ChatInput,
        DevLogPanel
    },
    props: {
        // 消息相关
        messages: {
            type: Array,
            default: () => []
        },
        isLoadingMessages: {
            type: Boolean,
            default: false
        },
        messageError: {
            type: String,
            default: null
        },
        activeConversationId: {
            type: String,
            default: null
        },
        // 容器类名，用于自定义样式
        containerClass: {
            type: String,
            default: ''
        },
        // 日志相关
        isDevLogVisible: {
            type: Boolean,
            default: false
        },
        devLogs: {
            // 日志可以是字符串（HTML）或数组
            type: [String, Array],
            default: ''
        },
        // 分享相关
        isShared: {
            type: Boolean,
            default: false
        },
        isOwner: {
            type: Boolean,
            default: false
        }
    },
    emits: [
        'message-sent',
        'message-start',
        'message-stream',
        'message-complete',
        'message-error',
        'message-interrupting',
        'toggle-dev-log',
        'message-marked-as-bad-case',
        'message-marked-as-good-case',
        'share-conversation'
    ],
    setup(_, { emit }) {
        // 消息处理方法
        const handleUserMessage = (message) => {
            emit('message-sent', message);
        };

        const handleAiMessageStart = (message) => {
            emit('message-start', message);
        };

        const handleAiMessageStream = (data) => {
            emit('message-stream', data);
        };

        const handleAiMessageComplete = (message) => {
            emit('message-complete', message);
        };

        const handleMessageError = (error) => {
            emit('message-error', error);
        };

        const handleMessageInterrupting = () => {
            emit('message-interrupting');
        };

        // 处理日志面板关闭
        const handleToggleDevLog = () => {
            emit('toggle-dev-log');
        };

        // 处理消息被标记为不良案例
        const handleMessageMarkedAsBadCase = (conversationId) => {
            emit('message-marked-as-bad-case', conversationId);
        };

        // 处理消息被标记为Good Case
        const handleMessageMarkedAsGoodCase = (conversationId) => {
            emit('message-marked-as-good-case', conversationId);
        };

        // 处理分享对话
        const handleShareConversation = (conversationId) => {
            emit('share-conversation', conversationId);
        };

        return {
            handleUserMessage,
            handleAiMessageStart,
            handleAiMessageStream,
            handleAiMessageComplete,
            handleMessageError,
            handleMessageInterrupting,
            handleToggleDevLog,
            handleMessageMarkedAsBadCase,
            handleMessageMarkedAsGoodCase,
            handleShareConversation
        };
    },
    template: `
        <div class="chat-content-container" :class="containerClass">
            <!-- Scrollable message area - only this area should scroll -->
            <div class="chat-scroll-container">
                <div class="chat-messages-content flex flex-col">
                    <div class="max-w-4xl w-full mx-auto px-4 sm:px-8 md:px-12 py-4 flex-1 flex flex-col">
                        <ChatArea
                            :messages="messages"
                            :is-loading="isLoadingMessages"
                            :error="messageError"
                            :conversation-id="activeConversationId"
                            :allowUnmarkBadCase="true"
                            class="w-full flex-1"
                            @message-marked-as-bad-case="handleMessageMarkedAsBadCase"
                            @message-marked-as-good-case="handleMessageMarkedAsGoodCase"
                            @share-conversation="handleShareConversation"
                        />
                    </div>
                </div>
            </div>

            <!-- 底部区域：包含输入框和日志面板 -->
            <div class="chat-bottom-area">
                <!-- Input area - 固定在底部 -->
                <div class="chat-input-area py-2">
                    <div class="px-2 sm:px-4 md:px-6 max-w-4xl mx-auto">
                        <!-- 在分享模式下，非所有者不能发送消息 -->
                        <div v-if="isShared && !isOwner" class="text-center text-sm text-base-content/60 py-2 border-t border-base-300/10">
                            您正在查看分享的对话，无法发送消息
                        </div>
                        <!-- 正常输入框 -->
                        <ChatInput
                            v-else
                            :conversation-id="activeConversationId"
                            :is-dev-log-visible="isDevLogVisible"
                            :messages="messages"
                            @message-sent="handleUserMessage"
                            @message-start="handleAiMessageStart"
                            @message-stream="handleAiMessageStream"
                            @message-complete="handleAiMessageComplete"
                            @message-error="handleMessageError"
                            @message-interrupting="handleMessageInterrupting"
                            @toggle-dev-log="handleToggleDevLog"
                        />
                    </div>
                </div>

                <!-- 日志面板 - 在输入框下方，可以展开/收起 -->
                <div class="chat-log-panel-container mb-2"
                     :style="{ maxHeight: isDevLogVisible ? '280px' : '0px' }">
                    <DevLogPanel
                        :is-visible="isDevLogVisible"
                        :logs="devLogs"
                        :active-conversation-id="activeConversationId"
                        @close="handleToggleDevLog"
                    />
                </div>
            </div>
        </div>
    `
};
