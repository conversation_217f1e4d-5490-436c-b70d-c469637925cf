/**
 * StatsCard Component
 *
 * Card component for displaying statistics with an optional icon
 * Extends BaseDashboardCard with specific styling for stats display
 */
import { computed } from 'vue';
import BaseDashboardCard from './BaseDashboardCard.js';

export default {
    name: 'StatsCard',
    components: {
        BaseDashboardCard
    },
    props: {
        /**
         * Card title
         */
        title: {
            type: String,
            default: ''
        },
        /**
         * Card subtitle
         */
        subtitle: {
            type: String,
            default: ''
        },
        /**
         * Main statistic value to display
         */
        value: {
            type: [Number, String],
            default: ''
        },
        /**
         * Optional description text
         */
        description: {
            type: String,
            default: ''
        },
        /**
         * SVG icon markup
         */
        icon: {
            type: String,
            default: ''
        },
        /**
         * Background color for icon container
         * Can be a Tailwind class or CSS color
         */
        iconBgColor: {
            type: String,
            default: 'bg-base-200 dark-mode-icon-bg'
        },
        /**
         * Icon color
         * Can be a Tailwind class or CSS color
         */
        iconColor: {
            type: String,
            default: 'text-primary'
        },
        /**
         * Whether the card is in a loading state
         */
        loading: {
            type: Boolean,
            default: false
        },
        /**
         * Additional CSS classes for the card
         */
        cardClass: {
            type: String,
            default: ''
        },
        /**
         * Card size (can be 'sm', 'md', 'lg', 'xl' or a Tailwind class like 'row-span-2')
         */
        size: {
            type: String,
            default: 'md'
        }
    },
    setup(props) {
        // Combine classes for the stats card
        const combinedCardClass = computed(() => {
            return `stats-card ${props.cardClass}`;
        });

        // Combine classes for the icon itself (not its container)
        const iconClass = computed(() => {
            // Reduced icon size for better proportions
            return `w-6 h-6 ${props.iconColor}`;
        });

        // Determine if we have an icon
        const hasIcon = computed(() => {
            return !!props.icon;
        });

        return {
            combinedCardClass,
            iconClass,
            hasIcon
        };
    },
    template: `
        <BaseDashboardCard
            :loading="loading"
            :card-class="combinedCardClass"
            :size="size"
        >
            <template #header>
                <div class="flex justify-between items-start w-full">
                    <div class="flex flex-col mr-4">
                        <div v-if="title" class="card-title text-base font-medium text-base-content/90 truncate">{{ title }}</div>
                        <div v-if="subtitle" class="card-subtitle text-xs text-base-content/70 mt-0.5">{{ subtitle }}</div>
                    </div>
                    <div v-if="!title && !subtitle && (hasIcon || $slots.icon)" class="flex-auto"></div> <!-- Spacer to push icon right if no title -->

                    <!-- Icon Container: Enhanced styling with smaller, more elegant design -->
                    <div
                        v-if="hasIcon || $slots.icon"
                        class="stats-icon-container flex-shrink-0 flex items-center justify-center p-2 rounded-xl shadow-sm"
                        :class="iconBgColor"
                    >
                        <slot name="icon">
                            <span :class="iconClass" v-html="icon"></span>
                        </slot>
                    </div>
                </div>
            </template>

            <template #default>
                <!-- Enhanced main content wrapper with improved spacing and alignment -->
                <div class="flex flex-col flex-grow justify-between pt-1">
                    <!-- Text Content Area with optimized spacing -->
                    <div>
                        <div class="stats-value text-2xl sm:text-3xl font-semibold text-base-content tracking-tight">
                            <slot name="value">{{ value }}</slot>
                        </div>
                        <div v-if="description || $slots.description" class="stats-description text-xs text-base-content/70 mt-0.5 pb-1">
                            <slot name="description">{{ description }}</slot>
                        </div>
                    </div>
                </div>

                <!-- Additional content slot with improved spacing -->
                <div v-if="$slots.default && $slots.default().length > 0" class="mt-2">
                    <slot></slot>
                </div>
            </template>
        </BaseDashboardCard>
    `
};
