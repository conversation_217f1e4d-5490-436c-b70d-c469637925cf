/**
 * Conversation Filters Component
 *
 * A reusable component for filtering conversations with username, date range, and advanced filters
 * Follows Apple/OpenAI-inspired aesthetic with clean design
 */
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import DateRangePicker from '../../common/components/DateRangePicker.js';
import { zLayoutHeader } from '../../utils/zIndex.js';
import { fetchUsers } from '../services/userService.js';
import { fetchAgents } from '../services/agentService.js';
import { fetchDepartmentsWithRetry } from '../services/departmentService.js';

export default {
    name: 'ConversationFilters',
    components: {
        DateRangePicker
    },
    props: {
        /**
         * Initial filters state
         */
        initialFilters: {
            type: Object,
            default: () => ({
                username: '',
                conversation_id: '',
                content_search: '',
                bad_case_filter: 'all',
                repair_status_filter: null,
                agent: '',
                first_level_department: '',
                filter_admin: false
            })
        },
        /**
         * Initial start date
         */
        initialStartDate: {
            type: String,
            default: ''
        },
        /**
         * Initial end date
         */
        initialEndDate: {
            type: String,
            default: ''
        }
    },
    emits: ['filter-change', 'filter-reset'],
    setup(props, { emit }) {
        // Filter state
        const filters = ref({...props.initialFilters});
        const startDate = ref(props.initialStartDate);
        const endDate = ref(props.initialEndDate);

        // Advanced filters dropdown state
        const showAdvancedFilters = ref(false);
        const advancedFiltersDropdown = ref(null);

        // Username dropdown state
        const showUsernameDropdown = ref(false);
        const usernameDropdown = ref(null);
        const userSearchInput = ref('');
        const users = ref([]);
        const isLoadingUsers = ref(false);

        // Agents data for advanced filters
        const agents = ref([]);
        const isLoadingAgents = ref(false);

        // Departments data for advanced filters
        const departments = ref([]);
        const isLoadingDepartments = ref(false);
        const departmentsLoaded = ref(false); // 缓存标记，避免重复加载

        // Window width for responsive behavior
        const windowWidth = ref(window.innerWidth);

        // Computed property to determine if desktop view
        const isDesktopView = computed(() => {
            return windowWidth.value >= 1024; // lg breakpoint
        });

        // Filtered users based on search input
        const filteredUsers = computed(() => {
            if (!userSearchInput.value) return users.value;

            const searchTerm = userSearchInput.value.toLowerCase();
            return users.value.filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                (user.email && user.email.toLowerCase().includes(searchTerm))
            );
        });



        // Load users from API
        const loadUsers = async () => {
            try {
                isLoadingUsers.value = true;
                const usersList = await fetchUsers();
                users.value = usersList;
            } catch (error) {
                console.error('Error loading users:', error);
            } finally {
                isLoadingUsers.value = false;
            }
        };

        // Load agents from API
        const loadAgents = async () => {
            try {
                isLoadingAgents.value = true;
                const agentsList = await fetchAgents();
                agents.value = agentsList;
            } catch (error) {
                console.error('Error loading agents:', error);
            } finally {
                isLoadingAgents.value = false;
            }
        };

        // Load departments from API with caching
        const loadDepartments = async () => {
            // 如果已经加载过，直接返回
            if (departmentsLoaded.value) {
                return;
            }

            try {
                isLoadingDepartments.value = true;
                const departmentsList = await fetchDepartmentsWithRetry();
                departments.value = departmentsList;
                departmentsLoaded.value = true; // 标记为已加载
            } catch (error) {
                console.error('Error loading departments:', error);
                // 在错误情况下设置为空数组，确保UI能正常显示
                departments.value = [];
                // 错误时不设置departmentsLoaded为true，允许重试
            } finally {
                isLoadingDepartments.value = false;
            }
        };

        // Select a user from the dropdown
        const selectUser = (user) => {
            filters.value.username = user.username;
            showUsernameDropdown.value = false;
            userSearchInput.value = user.username; // Set the input to the selected username
            applyFilters();
        };

        // Handle user search input
        const onUserSearchInput = () => {
            // If the input doesn't match the current filter, clear the filter
            if (userSearchInput.value !== filters.value.username) {
                filters.value.username = '';
            }

            // Show dropdown when typing
            showUsernameDropdown.value = true;

            // Load users if not loaded yet
            if (users.value.length === 0) {
                loadUsers();
            }
        };

        // Clear user search
        const clearUserSearch = () => {
            userSearchInput.value = '';
            filters.value.username = '';
            showUsernameDropdown.value = false;
            applyFilters();
        };



        // Handle click outside to close dropdowns
        const handleClickOutside = (event) => {
            if (advancedFiltersDropdown.value && !advancedFiltersDropdown.value.contains(event.target)) {
                showAdvancedFilters.value = false;
            }
            if (usernameDropdown.value && !usernameDropdown.value.contains(event.target)) {
                showUsernameDropdown.value = false;
            }
        };

        // Update window width on resize
        const handleResize = () => {
            windowWidth.value = window.innerWidth;
        };

        // Toggle advanced filters dropdown
        const toggleAdvancedFilters = () => {
            showAdvancedFilters.value = !showAdvancedFilters.value;
        };

        // Apply filters
        const applyFilters = () => {
            emit('filter-change', {
                filters: {...filters.value},
                startDate: startDate.value,
                endDate: endDate.value,
                resetPagination: false // 正常筛选不重置分页
            });
        };

        // Reset filters
        const resetFilters = () => {
            filters.value = {
                username: '',
                conversation_id: '',
                content_search: '',
                bad_case_filter: 'all',
                repair_status_filter: null,
                agent: '',
                first_level_department: '',
                filter_admin: false
            };
            startDate.value = '';
            endDate.value = '';
            userSearchInput.value = '';

            // 发出重置事件，通知父组件重置分页参数
            emit('filter-reset');

            // 同时发出filter-change事件，明确指示需要重置分页
            emit('filter-change', {
                filters: {...filters.value},
                startDate: startDate.value,
                endDate: endDate.value,
                resetPagination: true // 重置时需要重置分页
            });
        };

        // Apply filters and close dropdown
        const applyFiltersAndCloseDropdown = () => {
            applyFilters();
            showAdvancedFilters.value = false;
        };

        // Watch for changes in filters and dates
        watch([startDate, endDate], () => {
            // Auto-apply filters when dates change
            if (startDate.value && endDate.value) {
                applyFilters();
            }
        });

        // Add event listeners
        onMounted(() => {
            document.addEventListener('click', handleClickOutside);
            window.addEventListener('resize', handleResize);
            // Load users, agents and departments on component mount
            loadUsers();
            loadAgents();
            loadDepartments();
        });

        onUnmounted(() => {
            document.removeEventListener('click', handleClickOutside);
            window.removeEventListener('resize', handleResize);
        });

        return {
            filters,
            startDate,
            endDate,
            showAdvancedFilters,
            advancedFiltersDropdown,
            isDesktopView,
            toggleAdvancedFilters,
            applyFilters,
            resetFilters,
            applyFiltersAndCloseDropdown,
            zLayoutHeader,
            // Username dropdown
            showUsernameDropdown,
            usernameDropdown,
            userSearchInput,
            filteredUsers,
            isLoadingUsers,
            selectUser,
            onUserSearchInput,
            clearUserSearch,
            // Agents data
            agents,
            isLoadingAgents,
            // Departments data
            departments,
            isLoadingDepartments
        };
    },
    template: `
    <div class="flex flex-col sm:flex-row items-end sm:items-center gap-3">
        <!-- Username Filter Dropdown -->
        <div class="dropdown flex-none" style="max-width: 300px;" ref="usernameDropdown">
            <label class="input input-bordered input-sm h-9 flex items-center gap-2">
                <svg class="h-[1em] opacity-50" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g
                        stroke-linejoin="round"
                        stroke-linecap="round"
                        stroke-width="2.5"
                        fill="none"
                        stroke="currentColor"
                    >
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.3-4.3"></path>
                    </g>
                </svg>
                <input
                    type="text"
                    v-model="userSearchInput"
                    placeholder="用户名搜索"
                    class="grow text-sm placeholder:text-base-content/60"
                    @focus="showUsernameDropdown = true"
                    @input="onUserSearchInput"
                />
                <svg
                    v-if="filters.username || userSearchInput"
                    @click.stop="clearUserSearch"
                    class="h-4 w-4 opacity-50 hover:opacity-100 cursor-pointer"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                >
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
            </label>
            <div
                v-if="showUsernameDropdown"
                tabindex="0"
                class="dropdown-content menu p-2 shadow bg-base-100 dark:bg-base-100 rounded-box w-72 mt-2 border border-base-300 dark:border-base-content/20"
                :style="{ zIndex: zLayoutHeader + 1, maxWidth: '90vw', position: 'absolute', left: 0 }"
            >
                <!-- Loading State -->
                <div v-if="isLoadingUsers" class="flex justify-center py-4">
                    <span class="loading loading-spinner loading-sm"></span>
                </div>

                <!-- User List -->
                <div v-else-if="filteredUsers.length > 0" class="max-h-60 overflow-y-auto scrollbar-auto">
                    <div
                        v-for="user in filteredUsers"
                        :key="user.username"
                        class="px-2 py-2 hover:bg-base-200 dark:hover:bg-base-content/10 cursor-pointer rounded-md"
                        @click="selectUser(user)"
                    >
                        <div class="font-medium text-sm">{{ user.username }}</div>
                        <div class="text-xs opacity-70">{{ user.email || '' }}</div>
                    </div>
                </div>

                <!-- Empty State -->
                <div v-else class="text-center py-4 text-sm text-base-content/70">
                    {{ userSearchInput ? '没有找到匹配的用户' : '没有用户数据' }}
                </div>
            </div>
        </div>

        <!-- Date Range Picker -->
        <div class="flex-none" style="max-width: 300px;">
            <DateRangePicker
                v-model:startDate="startDate"
                v-model:endDate="endDate"
                label=""
                displayClass="input input-bordered input-sm h-9 w-full"
            />
        </div>

        <!-- Admin Filter Toggle -->
        <div class="flex items-center gap-2 bg-base-200/50 px-3 py-1.5 rounded-lg border border-base-300/50">
            <span class="text-sm font-medium text-base-content/80">过滤admin</span>
            <label class="cursor-pointer inline-flex items-center">
                <input
                    type="checkbox"
                    class="sr-only peer"
                    v-model="filters.filter_admin"
                    @change="applyFilters"
                />
                <div class="relative w-10 h-5 bg-base-300 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary"></div>
            </label>
        </div>

        <!-- Advanced Filters Dropdown -->
        <div class="dropdown flex-none ml-auto md:ml-0 advanced-filters-dropdown relative" ref="advancedFiltersDropdown">
            <label
                tabindex="0"
                class="btn btn-sm h-9 btn-outline border-base-300 flex items-center gap-2 hover:bg-base-200 hover:border-base-300 hover:text-base-content dark:border-base-content/20 dark:hover:border-base-content/30 dark:border"
                :class="{ 'bg-base-200 dark:bg-base-content/10': showAdvancedFilters }"
                @click="toggleAdvancedFilters"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="4" x2="4" y1="21" y2="14" />
                    <line x1="4" x2="4" y1="10" y2="3" />
                    <line x1="12" x2="12" y1="21" y2="12" />
                    <line x1="12" x2="12" y1="8" y2="3" />
                    <line x1="20" x2="20" y1="21" y2="16" />
                    <line x1="20" x2="20" y1="12" y2="3" />
                    <line x1="2" x2="6" y1="14" y2="14" />
                    <line x1="10" x2="14" y1="8" y2="8" />
                    <line x1="18" x2="22" y1="16" y2="16" />
                </svg>
                <span>筛选</span>
            </label>
            <div
                v-if="showAdvancedFilters"
                tabindex="0"
                class="dropdown-content menu p-4 shadow bg-base-100 dark:bg-base-100 rounded-box w-72 mt-2 border border-base-300 dark:border-base-content/20"
                :style="{ zIndex: zLayoutHeader + 1, maxWidth: '90vw', position: 'absolute', right: 0 }"
            >
                <div class="flex flex-col gap-4">
                    <!-- Conversation ID Filter -->
                    <div class="form-control">
                        <label class="label py-1">
                            <span class="label-text">会话ID</span>
                        </label>
                        <label class="input input-bordered input-sm h-9 flex items-center gap-2 dark:border-base-content/20 dark:border">
                            <input
                                type="text"
                                v-model="filters.conversation_id"
                                placeholder="输入会话ID"
                                class="grow text-sm placeholder:text-base-content/60"
                            />
                        </label>
                    </div>

                    <!-- Content Search -->
                    <div class="form-control">
                        <label class="label py-1">
                            <span class="label-text">内容搜索</span>
                        </label>
                        <label class="input input-bordered input-sm h-9 flex items-center gap-2 dark:border-base-content/20 dark:border">
                            <input
                                type="text"
                                v-model="filters.content_search"
                                placeholder="搜索对话内容"
                                class="grow text-sm placeholder:text-base-content/60"
                            />
                        </label>
                    </div>

                    <!-- Case Type Filter -->
                    <div class="form-control">
                        <label class="label py-1">
                            <span class="label-text">案例类型</span>
                        </label>
                        <select
                            v-model="filters.bad_case_filter"
                            class="select select-bordered select-sm h-9 w-full text-sm dark:border-base-content/20 dark:border"
                        >
                            <option value="all">全部</option>
                            <option value="only_normal">仅正常案例</option>
                            <option value="only_good_case">仅Good Case</option>
                            <option value="only_bad">仅Bad Case</option>
                        </select>
                    </div>

                    <!-- Repair Status Filter -->
                    <div class="form-control">
                        <label class="label py-1">
                            <span class="label-text">修复状态</span>
                        </label>
                        <select
                            v-model="filters.repair_status_filter"
                            class="select select-bordered select-sm h-9 w-full text-sm dark:border-base-content/20 dark:border"
                        >
                            <option :value="null">全部</option>
                            <option :value="0">未修复</option>
                            <option :value="1">已修复</option>
                            <option :value="2">暂不修复</option>
                        </select>
                    </div>

                    <!-- Agent Filter -->
                    <div class="form-control">
                        <label class="label py-1">
                            <span class="label-text">助手筛选</span>
                        </label>
                        <select
                            v-model="filters.agent"
                            class="select select-bordered select-sm h-9 w-full text-sm dark:border-base-content/20 dark:border"
                        >
                            <option value="">全部助手</option>
                            <option
                                v-for="agent in agents"
                                :key="agent.agent"
                                :value="agent.agent"
                            >
                                {{ agent.agent }}
                            </option>
                        </select>
                    </div>

                    <!-- Department Filter -->
                    <div class="form-control">
                        <label class="label py-1">
                            <span class="label-text">一级部门</span>
                            <span v-if="isLoadingDepartments" class="loading loading-spinner loading-xs ml-2"></span>
                        </label>
                        <select
                            v-model="filters.first_level_department"
                            class="select select-bordered select-sm h-9 w-full text-sm dark:border-base-content/20 dark:border"
                            :disabled="isLoadingDepartments"
                        >
                            <option value="">{{ isLoadingDepartments ? '加载中...' : '全部部门' }}</option>
                            <option v-if="!isLoadingDepartments && departments.length === 0" value="" disabled>
                                暂无部门数据
                            </option>
                            <option
                                v-for="department in departments"
                                :key="department"
                                :value="department"
                            >
                                {{ department }}
                            </option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-2 mt-4">
                        <!-- Reset Button -->
                        <button
                            class="btn btn-sm btn-outline border-base-300 dark:border-base-content/20 flex-1 h-9 hover:bg-base-200"
                            @click="resetFilters"
                        >
                            重置
                        </button>

                        <!-- Apply Filters Button -->
                        <button
                            class="btn btn-sm flex-1 h-9 bg-blue-500 hover:bg-blue-600 text-white border-blue-600 hover:border-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 dark:border-blue-800 dark:hover:border-blue-900"
                            @click="applyFiltersAndCloseDropdown"
                        >
                            应用筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    `
};
