import { reactive, ref, provide, onMounted } from 'vue';
import DashboardHeader from '../components/DashboardHeader.js';
import OverviewLayout from './OverviewLayout.js';
import TrendLayout from './TrendLayout.js';
import ConversationListLayout from './ConversationListLayout.js';
import ConversationDetailModal from '../components/modals/ConversationDetailModal.js';
import ShareModal from '../../chatbi/components/modals/ShareModal.js';
import ToastNotification from '../../common/components/ToastNotification.js';
import { useLayoutState } from '../../chatbi/composables/useLayoutState.js';
import { zLayoutHeader } from '../../utils/zIndex.js';
import { markBadCase, markGoodCase, fetchConversationById } from '../services/conversationService.js';

export default {
    name: 'DashboardLayout',
    components: {
        DashboardHeader,
        OverviewLayout,
        TrendLayout,
        ConversationListLayout,
        ConversationDetailModal,
        ShareModal,
        ToastNotification
    },
    setup() {
        // 获取用户信息
        const userInfo = reactive(window.userInfo || {
            name: '访客',
            avatar: '',
            isAdmin: false
        });

        // 使用布局状态组合式API
        const layoutState = useLayoutState();

        // 对话详情模态框状态
        const isModalOpen = ref(false);
        const selectedConversation = ref(null);

        // 分享模态框状态
        const isShareModalOpen = ref(false);
        const shareUrl = ref('');
        const isGeneratingShareLink = ref(false);



        // 打开对话详情模态框
        const openConversationDetail = (conversation) => {
            console.log('DashboardLayout: openConversationDetail called with', conversation);
            selectedConversation.value = conversation;
            isModalOpen.value = true;
        };

        // 关闭对话详情模态框
        const closeModal = () => {
            isModalOpen.value = false;
            selectedConversation.value = null;
        };

        // 处理对话更新事件
        const handleConversationUpdated = (updatedConversation) => {
            console.log('DashboardLayout: conversation updated', updatedConversation);
            // 如果需要，可以在这里触发刷新列表等操作
        };

        // 处理分享对话
        const handleShareConversation = async (conversationId) => {
            if (!conversationId) return;

            try {
                isGeneratingShareLink.value = true;
                isShareModalOpen.value = true;
                shareUrl.value = '';

                // 调用分享服务
                const response = await fetch('/api/share_conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        conversation_id: conversationId
                    })
                });

                if (!response.ok) {
                    throw new Error(`分享失败: ${response.status} ${response.statusText}`);
                }

                // 解析响应
                const text = await response.text();
                let result;

                try {
                    // 尝试解析 JSON
                    result = JSON.parse(text);
                } catch (jsonError) {
                    console.error('JSON 解析错误:', jsonError, '原始响应:', text);
                    throw new Error('服务器返回了无效的数据格式');
                }

                if (result && result.share_url) {
                    shareUrl.value = result.share_url;
                } else {
                    throw new Error('未获取到分享链接');
                }
            } catch (error) {
                console.error('分享对话失败:', error);
                isShareModalOpen.value = false;
                // 不显示 toast，由 ConversationDetailModal 组件负责显示
            } finally {
                isGeneratingShareLink.value = false;
            }
        };

        // 关闭分享模态框
        const closeShareModal = () => {
            isShareModalOpen.value = false;
        };

        // 处理标记 bad case
        const handleMarkBadCase = async (conversationId, username, email, isBadCase) => {
            if (!conversationId) return;

            try {
                // 调用 API 标记 bad case
                await markBadCase({
                    conversation_id: conversationId,
                    is_bad_case: isBadCase,
                    username,
                    email
                });

                // 成功标记，但不显示 toast，由 ConversationDetailModal 组件负责显示
                return true;
            } catch (error) {
                console.error('标记 bad case 失败:', error);
                return false;
            }
        };

        // 处理标记 good case
        const handleMarkGoodCase = async (conversationId, username, email, isGoodCase) => {
            if (!conversationId) return;

            try {
                // 调用 API 标记 good case
                await markGoodCase({
                    conversation_id: conversationId,
                    is_good_case: isGoodCase,
                    username,
                    email
                });

                // 成功标记，但不显示 toast，由 ConversationDetailModal 组件负责显示
                return true;
            } catch (error) {
                console.error('标记 good case 失败:', error);
                return false;
            }
        };

        // 提供全局状态给子组件
        provide('conversationModal', {
            openConversationDetail,
            closeModal
        });

        // 提供分享和标记 bad case/good case 功能给子组件
        provide('shareConversation', handleShareConversation);
        provide('markBadCase', handleMarkBadCase);
        provide('markGoodCase', handleMarkGoodCase);

        // 解析URL参数并自动打开对话详情
        const handleUrlParams = async () => {
            const urlParams = new URLSearchParams(window.location.search);
            const chatId = urlParams.get('chat');

            if (chatId) {
                console.log('Dashboard: 检测到chat参数:', chatId);
                try {
                    // 获取对话详情
                    const conversation = await fetchConversationById(chatId);
                    if (conversation) {
                        console.log('Dashboard: 成功获取对话详情，自动打开模态框');
                        openConversationDetail(conversation);

                        // 清除URL参数，避免刷新时重复打开
                        const newUrl = window.location.pathname;
                        window.history.replaceState({}, document.title, newUrl);
                    } else {
                        console.warn('Dashboard: 未找到对话ID为', chatId, '的对话');
                    }
                } catch (error) {
                    console.error('Dashboard: 获取对话详情失败:', error);
                }
            }
        };

        // 组件挂载时处理URL参数
        onMounted(() => {
            handleUrlParams();
        });

        return {
            // 用户信息
            userInfo,

            // 布局状态
            isDarkTheme: layoutState.isDarkTheme,
            handleToggleTheme: layoutState.toggleTheme,

            // 对话详情模态框状态
            isModalOpen,
            selectedConversation,
            closeModal,
            handleConversationUpdated,

            // 分享模态框状态
            isShareModalOpen,
            shareUrl,
            isGeneratingShareLink,
            closeShareModal,

            // Z-index 常量
            zLayoutHeader
        };
    },
    template: `
        <div class="h-screen flex flex-col min-w-0 overflow-y-auto scrollbar-auto">
            <!-- Centered Container for entire layout -->
            <div class="w-full max-w-7xl mx-auto px-2 sm:px-6 lg:px-8 flex flex-col h-auto">
                <!-- Header - Fixed at top -->
                <div class="sticky top-0 w-full bg-base-100" :style="{ zIndex: zLayoutHeader }">
                    <DashboardHeader
                        :user-info="userInfo"
                        :is-dark-theme="isDarkTheme"
                        @toggle-theme="handleToggleTheme"
                    />
                </div>

                <!-- Main Content -->
                <div class="flex-1 px-9 pt-6 overflow-visible">
                    <!-- Overview Section -->
                    <OverviewLayout />

                    <!-- Trend Analysis Section -->
                    <TrendLayout />

                    <!-- Conversation List Section -->
                    <ConversationListLayout />
                </div>
            </div>

            <!-- Global Conversation Detail Modal -->
            <ConversationDetailModal
                :is-open="isModalOpen"
                :conversation="selectedConversation"
                @close="closeModal"
                @conversation-updated="handleConversationUpdated"
            />

            <!-- Global Share Modal -->
            <ShareModal
                :is-open="isShareModalOpen"
                :share-url="shareUrl"
                :is-generating="isGeneratingShareLink"
                @close="closeShareModal"
            />
        </div>
    `
};