import os
import requests
import jwt
import json
from datetime import datetime, timedelta
import urllib.parse
from flask import redirect, make_response, session, request, render_template, jsonify
from functools import wraps
from typing import Optional, Dict, Any

from src.services.xianmudb.query_service import execute_business_query
from src.utils.logger import logger
from utils.user_utils import get_api_token
from src.utils.in_memory_cache import in_memory_cache
from src.db.connection import execute_db_query as _execute_db_query
from src.services.auth.user_session_service import user_session_service
# UserSessionRepository import已移除，相关建表逻辑已移至 init.sql

# 飞书应用的 App ID 和 App Secret
APP_ID = os.getenv("FEISHU_APP_ID")
APP_SECRET = os.getenv("FEISHU_APP_SECRET")
JWT_SECRET = os.getenv("JWT_SECRET", "your-secret-key-change-in-production")

if not APP_ID or not APP_SECRET:
    raise ValueError("飞书应用的 App ID 和 App Secret 未配置")

# 本地调试：http://127.0.0.1:5700
HOST_NAME = os.getenv(
    "CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net"
)
MINUTES_TO_FORCE_REFRESH_TOKEN = int(os.getenv("MINUTES_TO_FORCE_REFRESH_TOKEN", 10))

logger.info(f"HOST_NAME: {HOST_NAME}")

scope = [
    "offline_access",
    "contact:user.email:readonly",
    "contact:department.base:readonly",
    "contact:user.base:readonly",
    "contact:user.employee:readonly",
    "contact:contact.base:readonly",
    "docs:document:import",
    "drive:drive",
    "drive:file",
    "drive:file:upload",
    "base:app:create",
    "bitable:app",
    "aily:file:write",
    "wiki:wiki:readonly",
    "docx:document:readonly",
    "docs:document.content:read",
    "im:message",
    "search:message",
]

scope_encoded = urllib.parse.quote_plus(" ".join(scope))


# 注意：用户表结构创建已移至 init.sql 文件
# 该模块专注于用户认证逻辑，不再包含建表逻辑

# Token刷新由后台服务统一处理，避免并发冲突


def generate_jwt_token(session_id: str) -> str:
    """
    生成JWT token
    """
    payload = {
        'session_id': session_id,
        'exp': datetime.utcnow() + timedelta(days=30),  # 30天过期
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')


def decode_jwt_token(token: str) -> Optional[dict]:
    """
    解码JWT token，返回payload字典
    """
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("JWT token已过期")
        return None
    except jwt.InvalidTokenError:
        logger.warning("无效的JWT token")
        return None


@in_memory_cache(expire_seconds=600)  # 缓存10分钟
def get_user_info_by_open_id(open_id: str) -> Optional[Dict[str, Any]]:
    """
    根据open_id从数据库获取用户信息
    """
    # MySQL使用%s作为参数占位符，包含union_id字段
    sql = """
    SELECT id, name, email, user_id, job_title, open_id, union_id, avatar, last_login_time
    FROM user 
    WHERE open_id = %s
    """
        
    try:
        result = _execute_db_query(sql, (open_id,), fetch='all')
        if result and len(result) > 0:
            row = result[0]
            # 处理字典格式的结果（MySQL connector返回字典）
            if isinstance(row, dict):
                # 处理avatar字段：如果是JSON字符串则尝试反序列化，否则直接使用字符串URL
                avatar_data = row['avatar']
                if avatar_data and isinstance(avatar_data, str):
                    # 先尝试作为JSON解析，如果失败则当作普通字符串URL处理
                    try:
                        parsed_avatar = json.loads(avatar_data)
                        # 如果解析成功且是字典，提取avatar_thumb或其他字段
                        if isinstance(parsed_avatar, dict):
                            avatar_data = (parsed_avatar.get("avatar_thumb") or
                                         parsed_avatar.get("avatar_middle") or
                                         parsed_avatar.get("avatar_big") or
                                         parsed_avatar.get("avatar_origin"))
                        else:
                            avatar_data = parsed_avatar
                    except (json.JSONDecodeError, TypeError):
                        # 如果JSON解析失败，说明是普通字符串URL，直接使用
                        pass
                
                user_data = {
                    'id': row['id'],
                    'name': row['name'],
                    'email': row['email'],
                    'user_id': row['user_id'],
                    'job_title': row['job_title'],
                    'open_id': row['open_id'],
                    'union_id': row['union_id'],
                    'avatar': avatar_data,
                    'last_login_time': row['last_login_time']
                }
            else:
                # 处理元组格式的结果（向后兼容）
                # 处理avatar字段：如果是JSON字符串则尝试反序列化，否则直接使用字符串URL
                avatar_data = row[7]
                if avatar_data and isinstance(avatar_data, str):
                    # 先尝试作为JSON解析，如果失败则当作普通字符串URL处理
                    try:
                        parsed_avatar = json.loads(avatar_data)
                        # 如果解析成功且是字典，提取avatar_thumb或其他字段
                        if isinstance(parsed_avatar, dict):
                            avatar_data = (parsed_avatar.get("avatar_thumb") or
                                         parsed_avatar.get("avatar_middle") or
                                         parsed_avatar.get("avatar_big") or
                                         parsed_avatar.get("avatar_origin"))
                        else:
                            avatar_data = parsed_avatar
                    except (json.JSONDecodeError, TypeError):
                        # 如果JSON解析失败，说明是普通字符串URL，直接使用
                        pass
                
                user_data = {
                    'id': row[0],
                    'name': row[1],
                    'email': row[2],
                    'user_id': row[3],
                    'job_title': row[4],
                    'open_id': row[5],
                    'union_id': row[6],
                    'avatar': avatar_data,
                    'last_login_time': row[8]
                }
            
            # 如果有union_id，获取summerfarm_api_token
            if user_data.get('union_id'):
                api_token = get_api_token(union_id=user_data['union_id'])
                user_data['summerfarm_api_token'] = api_token
            
            return user_data
        return None
    except Exception as e:
        logger.error(f"根据open_id获取用户信息失败: {e}", exc_info=True)
        return None





def refresh_token_and_get_new_access_token(refresh_token: str) -> tuple[Optional[str], Optional[str]]:
    """
    使用refresh_token刷新access_token
    """
    token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
    payload = {
        "grant_type": "refresh_token",
        "client_id": APP_ID,
        "client_secret": APP_SECRET,
        "refresh_token": refresh_token,
    }
    try:
        response = requests.post(token_url, json=payload, timeout=10)
        result = response.json()
        logger.info(f"刷新token响应: {result}")

        if "access_token" in result:
            return result["access_token"], result.get("refresh_token", refresh_token)
        else:
            logger.error(f"刷新token失败: {result}")
            return None, None
    except Exception as e:
        logger.error(f"刷新token请求失败: {e}", exc_info=True)
        return None, None


# safe_refresh_session_token函数已移除，token刷新由后台服务统一处理


@in_memory_cache(expire_seconds=1200)  # 缓存20分钟
def get_user_admin_id(user_name, email):
    """
    根据用户名和邮箱获取admin_id
    """
    # 使用字符串格式化构建SQL查询，使用repr()来安全地转义字符串参数
    sql = f"""
    SELECT `admin_id`,`realname`,`username`
    FROM `admin`
    WHERE `is_disabled` = 0
      AND (username = {repr(email)} OR realname = {repr(user_name)})
    ORDER BY
      CASE WHEN username = {repr(email)} THEN 0 ELSE 1 END
    LIMIT 1;
    """
    # 这里使用execute_business_query，它连接的是业务数据库（MySQL），不是ChatBI数据库
    # 所以不需要考虑SQLite兼容性
    result = execute_business_query(sql)
    if result and result.data and len(result.data) > 0:
        logger.info(f"User {user_name} ({email}) found in database:{result}")
        return result.data[0][0]
    logger.error(f"User {user_name} ({email}) not found in database.")
    return None


def get_user_info_from_feishu(access_token: str) -> Optional[Dict[str, Any]]:
    """
    从飞书获取用户信息
    """
    user_info_url = "https://open.feishu.cn/open-apis/authen/v1/user_info"
    headers = {"Authorization": f"Bearer {access_token}"}

    try:
        user_info_response = requests.get(user_info_url, headers=headers, timeout=10)
        if user_info_response.status_code == 200:
            user_info = user_info_response.json().get("data")
            logger.info(f"获取到的用户信息: {user_info}")
            
            email = user_info.get("enterprise_email", "unknown-email")
            if user_info.get("email") is None or "@" not in user_info.get("email", ""):
                user_info["email"] = email
            
            # 获取头像信息，优先使用avatar_thumb
            avatar = user_info.get("avatar_thumb") or user_info.get("avatar_middle") or user_info.get("avatar_big")
            if avatar:
                user_info["avatar"] = avatar

            # 获取job title
            open_id = user_info.get("open_id")
            if open_id:
                job_title_url = f"https://open.feishu.cn/open-apis/contact/v3/users/{open_id}?department_id_type=open_department_id&user_id_type=open_id"
                job_title_response = requests.get(job_title_url, headers=headers, timeout=10)
                if job_title_response.status_code == 200:
                    job_title_data = job_title_response.json()
                    job_title = job_title_data.get("data", {}).get("user", {}).get("job_title")
                    if job_title:
                        user_info["job_title"] = job_title

            # 获取admin_id
            admin_id = get_user_admin_id(
                user_name=user_info.get("name"), 
                email=user_info.get("email")
            )
            if admin_id:
                user_info["admin_id"] = admin_id
            else:
                logger.error(f"未获取到 admin_id, user_info:{user_info}")
                return None

            # 获取API token
            union_id = user_info.get("union_id")
            if union_id:
                api_token = get_api_token(union_id=union_id)
                user_info["summerfarm_api_token"] = api_token

            return user_info
        else:
            logger.error(f"获取用户信息失败: {user_info_response.text}")
            return None
    except Exception as e:
        logger.error(f"获取用户信息异常: {e}", exc_info=True)
        return None


def upsert_user_info(user_info: Dict[str, Any]):
    """
    插入或更新用户信息到数据库
    """
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    open_id = user_info.get("open_id")

    try:
        # MySQL版本使用ON DUPLICATE KEY UPDATE，包含union_id和first_level_department字段
        sql = """
        INSERT INTO `user` (`name`, `email`, `user_id`, `job_title`, `first_level_department`, `open_id`, `union_id`, `avatar`, `last_login_time`)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            `name`=VALUES(`name`),
            `email`=VALUES(`email`),
            `user_id`=VALUES(`user_id`),
            `job_title`=VALUES(`job_title`),
            `first_level_department`=VALUES(`first_level_department`),
            `union_id`=VALUES(`union_id`),
            `avatar`=VALUES(`avatar`),
            `last_login_time`=VALUES(`last_login_time`);
        """
        # 处理avatar字段：如果是字符串URL直接使用，如果是字典则取avatar_640或avatar_240
        avatar_data = user_info.get("avatar")
        if avatar_data:
            if isinstance(avatar_data, str):
                # 如果是字符串URL，直接使用
                avatar_json = avatar_data
            elif isinstance(avatar_data, dict):
                # 如果是字典，则取avatar_640或avatar_240
                avatar_json = avatar_data.get("avatar_640", avatar_data.get("avatar_240"))
            else:
                avatar_json = None
        else:
            avatar_json = None

        if avatar_json and "{" in avatar_json:
            # '{"avatar_72": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp", "avatar_240": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp", "avatar_640": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp", "avatar_origin": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp"}'
            avatar_json = json.loads(avatar_json)
            avatar_json = avatar_json.get("avatar_640", avatar_json.get("avatar_240"))
        
        params = (
            user_info.get("name"),
            user_info.get("email"),
            user_info.get("user_id"),
            user_info.get("job_title"),
            user_info.get("first_level_department"),  # 添加first_level_department参数
            open_id,
            user_info.get("union_id"),  # 添加union_id参数
            avatar_json,
            current_time
        )
        _execute_db_query(sql, params, fetch=None)
            
        logger.info(f"用户信息upsert成功: open_id={open_id}, union_id={user_info.get('union_id')}")
        # 清除缓存
        from src.utils.in_memory_cache import clear_cache
        clear_cache(get_user_info_by_open_id.__qualname__)
    except Exception as e:
        logger.error(f"用户信息upsert失败: {e}", exc_info=True)


def update_user_first_level_department(email: str, first_level_department: str) -> bool:
    """
    更新用户的一级部门信息

    Args:
        email: 用户的邮箱
        first_level_department: 一级部门名称

    Returns:
        bool: 更新是否成功
    """
    try:
        sql = """
        UPDATE `user`
        SET `first_level_department` = %s
        WHERE `email` = %s 
        AND (`first_level_department` IS NULL OR `first_level_department` = '');;
        """

        _execute_db_query(sql, (first_level_department, email), fetch=None)
        logger.info(f"用户一级部门更新成功: email={email}, first_level_department={first_level_department}")

        # 清除缓存
        from src.utils.in_memory_cache import clear_cache
        clear_cache(get_user_info_by_open_id.__qualname__)

        return True
    except Exception as e:
        logger.error(f"用户一级部门更新失败: email={email}, error={e}", exc_info=True)
        return False


def login(destination_path: str = None):
    """
    处理用户登录逻辑
    """
    jwt_token = request.cookies.get("jwt_token")
    
    # 检查JWT token是否存在且有效
    if jwt_token:
        payload = decode_jwt_token(jwt_token)
        if payload:
            session_id = payload.get('session_id')
            if session_id:
                # 通过session服务获取用户session
                user_session = user_session_service.get_session_by_id(session_id)
            if user_session:
                    # 不在这里刷新token，由后台服务统一处理
                    # 只检查token是否已经过期，如果过期则需要重新登录
                    if user_session.is_access_token_expired():
                        logger.warning(f"Access token已过期，需要重新登录: session_id={session_id}")
                        # Token已过期，跳转到重新授权流程
                        request_host = request.host_url.rstrip("/")
                        redirect_uri = f"{request_host}/callback"
                        state_param = urllib.parse.quote_plus(destination_path if destination_path else "/")
                        auth_url = f"https://accounts.feishu.cn/open-apis/authen/v1/authorize?app_id={APP_ID}&redirect_uri={redirect_uri}&response_type=code&scope={scope_encoded}&state={state_param}"
                        logger.info(f"重定向到飞书授权URL: {auth_url}")
                        return redirect(auth_url)
                    
                    # 获取用户信息并设置到session中
                    user_info = get_user_info_by_open_id(user_session.open_id)
                    if user_info:
                        session["user_info"] = user_info
                        # 用户已登录，重定向到dashboard
                        return redirect(destination_path if destination_path else '/dashboard')
                    else:
                        logger.warning(f"无法获取用户信息: open_id={user_session.open_id}")
    
    # JWT无效或刷新失败，重新授权
    request_host = request.host_url.rstrip("/")
    redirect_uri = f"{request_host}/callback"
    state_param = urllib.parse.quote_plus(destination_path if destination_path else "/")
    auth_url = f"https://accounts.feishu.cn/open-apis/authen/v1/authorize?app_id={APP_ID}&redirect_uri={redirect_uri}&response_type=code&scope={scope_encoded}&state={state_param}"
    logger.info(f"重定向到飞书授权URL: {auth_url}")
    return redirect(auth_url)


def callback():
    """
    处理飞书授权回调
    """
    request_host = request.host_url.rstrip("/")
    code = request.args.get("code")
    state = request.args.get("state")
    destination_path = urllib.parse.unquote_plus(state) if state else "/"
    
    if not code:
        logger.error("未收到授权码")
        return render_template(
            "login_redirect.html", 
            success=False, 
            error_message="授权失败：未收到授权码"
        )

    logger.info(f"收到飞书授权码: {code}")
    redirect_uri = f"{request_host}/callback"

    try:
        # 请求access_token
        token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
        payload = {
            "grant_type": "authorization_code",
            "client_id": APP_ID,
            "client_secret": APP_SECRET,
            "code": code,
            "redirect_uri": redirect_uri,
        }

        response = requests.post(token_url, json=payload, timeout=10)
        token_response = response.json()
        logger.info(f"飞书token响应: {token_response}")

        if "error" in token_response or "access_token" not in token_response:
            error_msg = "飞书授权失败"
            logger.error(f"获取飞书access token失败: {token_response}")
            return render_template(
                "login_redirect.html",
                success=False,
                error_message=error_msg
            )

        # 获取用户信息
        access_token = token_response["access_token"]
        refresh_token = token_response.get("refresh_token")
        user_info = get_user_info_from_feishu(access_token)

        if not user_info:
            return render_template(
                "login_redirect.html",
                success=False,
                error_message="您暂无权限访问，请联系管理员：唐鹏处理"
            )

        # 计算access token过期时间
        expires_at = datetime.now() + timedelta(hours=2)
        
        # 保存用户信息到数据库
        upsert_user_info(user_info)
        
        # 创建用户session
        session_id = user_session_service.create_user_session(
            open_id=user_info.get("open_id"),
            refresh_token=refresh_token,
            access_token=access_token,
            access_token_expires_at=expires_at
        )

        if not session_id:
            return render_template(
                "login_redirect.html",
                success=False,
                error_message="创建用户会话失败，请重试"
            )

        # 不在这里刷新token，让后台服务统一处理token刷新
        logger.info(f"用户登录成功，session创建完成: session_id={session_id}")
        
        # 生成JWT token（包含session_id）
        jwt_token = generate_jwt_token(session_id)
        
        # 处理chat参数
        chat_param = request.args.get('chat')
        if chat_param:
            parsed_destination = urllib.parse.urlparse(destination_path)
            query_params = urllib.parse.parse_qs(parsed_destination.query)
            query_params['chat'] = [chat_param]
            new_query = urllib.parse.urlencode(query_params, doseq=True)
            destination_path = f"{parsed_destination.path}?{new_query}"

        # 设置JWT token到cookie
        final_redirect_url = f"{request_host}{destination_path}"
        resp = redirect(final_redirect_url)
        
        is_secure = request.scheme == "https"
        cookie_kwargs = {
            "secure": is_secure,
            "samesite": "None" if is_secure else None,
            "max_age": 30 * 24 * 60 * 60  # 30天
        }
        
        resp.set_cookie("jwt_token", jwt_token, **cookie_kwargs)
        session["user_info"] = user_info
        
        logger.info(f"用户登录成功: {user_info.get('name')}")
        return resp

    except requests.RequestException as e:
        logger.exception(f"请求飞书API时发生错误: {str(e)}")
        return render_template(
            "login_redirect.html",
            success=False,
            error_message=f"连接飞书服务器失败: {str(e)}"
        )
    except Exception as e:
        logger.exception(f"处理飞书回调时发生未知错误: {str(e)}")
        return render_template(
            "login_redirect.html",
            success=False,
            error_message=f"处理授权时发生错误: {str(e)}"
        )


def login_required(f):
    """
    登录验证装饰器
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        jwt_token = request.cookies.get("jwt_token")
        is_json_request = "application/json" in request.headers.get("Accept", "")
        
        # 检查JWT token
        if not jwt_token:
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 401)
            return redirect(build_login_redirect_url())
        
        # 解码JWT token
        payload = decode_jwt_token(jwt_token)
        if not payload:
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 401)
            return redirect(build_login_redirect_url())
        
        session_id = payload.get('session_id')
        if not session_id:
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 401)
            return redirect(build_login_redirect_url())
        
        # 通过session服务获取用户session
        user_session = user_session_service.get_session_by_id(session_id)
        if not user_session:
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 401)
            return redirect(build_login_redirect_url())
        
        # 获取用户信息
        user_info = get_user_info_by_open_id(user_session.open_id)
        if not user_info:
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 401)
            return redirect(build_login_redirect_url())
        
        # 不在这里刷新token，由后台服务统一处理
        # 只检查token是否已经过期，如果过期则需要重新登录
        if user_session.is_access_token_expired():
            logger.warning(f"Access token已过期，需要重新登录: session_id={session_id}")
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 401)
            return redirect(build_login_redirect_url())
        
        # 将用户信息存储到session中
        session["user_info"] = user_info
        return f(*args, **kwargs)
    
    return decorated_function


def build_login_redirect_url():
    """
    构建登录重定向URL
    """
    full_url = request.url
    parsed_url = urllib.parse.urlparse(full_url)
    path_and_query = parsed_url.path
    if parsed_url.query:
        path_and_query += f"?{parsed_url.query}"
    next_param = urllib.parse.quote_plus(path_and_query)
    return f"{HOST_NAME}/login?next={next_param}"


# 注意：数据库表初始化已移至 init.sql 文件
