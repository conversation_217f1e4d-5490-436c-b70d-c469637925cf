"""
用户查询推荐机器人模块。
"""
from typing import Dict, Any, List, Optional

from agents import Agent, Model, Runner

from src.models.user_info_class import UserInfo # 假设 UserInfo 用于上下文类型
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.model_provider import get_fast_model
from src.utils.logger import logger
import re

class UserQueryRecommendationBot(BaseBot):
    """
    用户查询推荐机器人。
    该机器人调用 LITE_LLM_FAST_MODEL，根据用户聊天历史和他人聊天历史，
    为用户生成针对性的问题推荐。
    """

    def __init__(self, user_info: Dict[str, Any], count: int = 6, word_limit: int = 100):
        """
        初始化用户查询推荐机器人。

        Args:
            user_info (Dict[str, Any]): 用户信息字典。
        """
        super().__init__(user_info)
        self.count = count
        self.word_limit = word_limit
        # 此处可以添加特定于此机器人的初始化代码

    def get_description(self) -> str:
        """
        获取机器人的描述信息。

        Returns:
            str: 机器人的描述。
        """
        return "我是一个用户查询推荐机器人，可以根据您的历史提问和大家的常见问题，为您推荐一些您可能感兴趣的新问题方向。"

    def _create_recommendation_prompt_text(self) -> str:
        """
        创建用于问题推荐的LLM提示文本。

        Returns:
            str: LLM提示文本。
        """
        prompt = f"""
你是用户问题推荐助手。
你的核心任务是根据用户提供的聊天记录，生成{self.count}条用户可能感兴趣的、全新的、有针对性的推荐问题。

请严格遵守以下规则：
1.  仔细分析“用户的最新消息”和“其他用户的消息”部分。这些信息是您生成推荐的唯一依据。
2.  基于这些信息，预测用户接下来可能会对哪些相关或扩展性话题感兴趣。
3.  生成正好{self.count}条推荐问题。
4.  每条推荐问题必须是完整的句子，严格控制在{self.word_limit}个汉字以内（包含标点符号）。
5.  推荐问题应该是全新的、具有探索性的，可以是对已有问题的改写、改进、扩展。
6.  推荐问题应该能够启发用户思考，引导他们探索新的知识领域或深入了解相关主题。
7.  请直接返回{self.count}条问题，每条问题占一行。不要添加任何编号 (如 1., 2.)、引导性短语 (如 "以下是一些推荐问题：") 或任何其他解释性文字。
8.  推荐的核心原则：生成的问题要能够让AI易于理解，不产生歧义。

输入示例格式:
    ## 用户的最新消息:
    1. 我的客户中，过去30天购买了安佳淡奶油的有哪些？列出他们的详细信息。
    2. 杭州市过去7天新增了多少新注册的门店？
    3. 我的客户中，有哪些客户在过去6个月曾经购买过10单以上，但是最近30天都未再来下单的？列出他们的详细信息（流失用户分析）。
    4. 杭州市过去30天下单数最大的客户是那几个？
    5. 门店ID=15062 的商户下单量最大的商品是哪些？列出他过去30天的下单商品详细信息
    6. 安佳淡奶油在全国各个仓库的库存情况是怎样的？
    7. 门店ID=15062 的最近30天内的拜访记录，深度分析它为什么流失了。
其他用户的消息也类似

输出示例格式 (严格按照此格式，每行一个问题，无多余字符):
【输出要求】
1. 一定要推荐非常具体、明确、结构化的问题，要能够直接作为用户的问题提交给AI助手回答。
2. 每个问题都需要包含这几个要素：时间范围、具体对象、分析维度、字段集、排序方式等，结果不得少于30个字，也不要超过{self.word_limit}个字。
3. 尽量以第一人称提问，以“我”或者“我的团队”开头。
【输出举例】
我的客户有哪些客户在过去60天买过protag纯牛奶整箱的，列出他们的店名和手机号码，加上购买protag纯牛奶整箱的件数。
"""
        return prompt.strip()

    def create_agent(self, model: Model = None) -> Agent:
        """
        创建用于生成问题推荐的Agent实例。

        Args:
            model (Model, optional): Agent使用的语言模型。默认为快速模型。

        Returns:
            Agent: 配置好的Agent实例。
        """
        if model is None:
            model = get_fast_model()
            
        instruction = self._create_recommendation_prompt_text()
        
        # 记录部分指令用于调试
        logger.info(f"UserQueryRecommendationBot instruction (first 200 chars): {instruction[:200]}...")

        # 假设 UserInfo 是标准的上下文类型，与 MasterControllerBot 保持一致
        agent = Agent[UserInfo](
            name="问题推荐助手",
            instructions=instruction,
            model=model,  # 使用指定的快速模型
            # 此Agent专注于文本生成，不需要额外的工具或切换能力 (handoffs)
        )
        return agent

    async def get_recommendations(
        self, 
        current_user_messages: List[str], 
        other_users_messages: List[str],
    ) -> List[str]:
        """
        异步获取问题推荐列表。

        此方法会调用LLM Agent来生成推荐。调用者需要负责从数据库或其他来源
        获取用户的聊天记录，并提取消息内容列表传入。

        Args:
            current_user_messages (List[str]): 当前用户最新的消息内容列表。
            other_users_messages (List[str]): 其他用户的一些消息内容列表。

        Returns:
            List[str]: 生成的推荐问题列表 (最多self.count条，每条不超过self.word_limit个字)。若出错则返回空列表。
        """
        agent = self.create_agent()

        # 构建输入给Agent的文本
        input_parts = []
        if current_user_messages:
            input_parts.append("## 用户的最新消息:\n" + "\n".join(f"- {msg}" for msg in current_user_messages))
        else:
            input_parts.append("## 用户的最新消息:\n- (当前用户近期无消息)")

        if other_users_messages:
            input_parts.append("\n\n## 其他用户的消息:\n" + "\n".join(f"- {msg}" for msg in other_users_messages))
        else:
            input_parts.append("\n\n## 其他用户的消息:\n- (无其他用户消息参考)")
            
        agent_input_text = "".join(input_parts)
        
        logger.info(f"UserQueryRecommendationBot agent input (first 300 chars): {agent_input_text[:300]}...")

        try:
            # 运行Agent并获取响应
            response_message = await Runner.run(agent, agent_input_text)
            
            # 解析LLM的响应内容
            # 假设LLM按要求每行返回一个问题
            raw_recommendations = f"{response_message.final_output}".split('\n')
            logger.info(f"Raw recommendations: {raw_recommendations}")
            
            valid_recommendations = []
            for rec in raw_recommendations:
                stripped_rec = rec.strip()
                # 过滤空行和过长的问题
                if stripped_rec and len(stripped_rec) <= self.word_limit:
                    # 去掉markdown的list前缀，仅去掉开头的‘-’或数字加点（如'1. '）
                    stripped_rec = re.sub(r'^(?:\d+\.\s*|\-\s*)', '', stripped_rec)
                    valid_recommendations.append(stripped_rec)
                else:
                    logger.warning(f"Invalid recommendation skipped: {stripped_rec}") 
            
            # 确保返回count条推荐
            final_recommendations = valid_recommendations[:self.count]
            if len(final_recommendations) < self.count:
                logger.warning(f"Not enough recommendations generated, returning {len(final_recommendations)} instead of {self.count}")
            
            logger.info(f"Generated {len(final_recommendations)} recommendations: {final_recommendations} (count={self.count})")
            return final_recommendations
        except Exception as e:
            logger.error(f"Error during UserQueryRecommendationBot agent execution: {e}", exc_info=True)
            return []