"""
Good case service module.

This module provides business logic for managing good cases.
"""

import functools
import concurrent.futures
from typing import Optional

from src.utils.logger import logger
from src.repositories.chatbi.good_case import (
    mark_conversation_as_good_case,
    get_conversation_good_case_status,
)

# 创建专用线程池用于异步发送飞书通知
_NOTIFICATION_EXECUTOR = concurrent.futures.ThreadPoolExecutor(
    max_workers=5,  # 通知发送不需要太多线程
    thread_name_prefix="good_case_notification_worker"
)


def feishu_notification_decorator(func):
    """
    装饰器：在标记 good case 后异步发送飞书通知
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 执行原函数
        result = func(*args, **kwargs)

        # 如果标记成功，异步发送飞书通知
        if result:
            conversation_id = kwargs.get('conversation_id', args[0] if args else None)
            is_good_case = kwargs.get('is_good_case', args[1] if len(args) > 1 else True)
            user_name = kwargs.get('user_name', args[2] if len(args) > 2 else None)

            # 异步提交通知发送任务到线程池，不等待结果
            _NOTIFICATION_EXECUTOR.submit(
                _send_good_case_notification_async,
                conversation_id,
                is_good_case,
                user_name
            )

            logger.info(f"已异步提交Good Case飞书通知发送任务，对话ID: {conversation_id}")

        return result
    return wrapper


def _send_good_case_notification_async(conversation_id: str, is_good_case: bool, user_name: Optional[str]) -> None:
    """
    异步发送Good Case通知到飞书群聊（在线程池中执行）

    Args:
        conversation_id (str): 对话ID
        is_good_case (bool): 是否为good case
        user_name (Optional[str]): 标记用户名
    """
    try:
        # 获取所有用户消息内容并拼接
        from src.utils.conversation_utils import get_all_user_messages_content, get_last_assistant_message_content
        all_user_messages_content = get_all_user_messages_content(conversation_id)

        # 获取最后一条assistant回复内容
        last_assistant_content = get_last_assistant_message_content(conversation_id)

        # 获取对话所属用户信息
        from src.repositories.chatbi.history import check_conversation_owner
        conversation_owner = check_conversation_owner(conversation_id)
        conversation_user_name = None
        if conversation_owner:
            conversation_user_name = conversation_owner.get('username')

        # 导入飞书通知函数（延迟导入避免循环依赖）
        from src.services.feishu.message_apis import send_good_case_notification, send_good_case_unmark_notification

        notification_func = send_good_case_notification if is_good_case else send_good_case_unmark_notification
        action = "标记" if is_good_case else "取消标记"

        # 传递标记用户、对话用户信息、用户消息内容和最后一条assistant回复，与Bad Case保持一致
        notification_sent = notification_func(
            conversation_id,
            user_name,  # 标记用户
            all_user_messages_content,  # 用户消息内容，与Bad Case保持一致
            conversation_user_name,  # 对话用户
            last_assistant_content  # 最后一条assistant回复
        )

        if notification_sent:
            logger.info(f"异步发送Good Case{action}通知成功，对话ID: {conversation_id}")
        else:
            logger.warning(f"异步发送Good Case{action}通知失败，对话ID: {conversation_id}")

    except Exception as e:
        action = "标记" if is_good_case else "取消标记"
        logger.error(f"异步发送Good Case{action}通知时发生异常: {str(e)}", exc_info=True)


@feishu_notification_decorator
def mark_good_case(conversation_id: str, is_good_case: bool = True, user_name: str = None) -> bool:
    """
    Mark or unmark a conversation as a good case.

    Args:
        conversation_id (str): The ID of the conversation
        is_good_case (bool, optional): Whether to mark as good case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the good case. Used for notification. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    action = "marked" if is_good_case else "unmarked"
    logger.info(f"User {action} conversation {conversation_id} as good case, user_name: {user_name}")
    return mark_conversation_as_good_case(conversation_id, is_good_case, marked_by=user_name)


def is_good_case(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a good case.

    Args:
        conversation_id (str): The ID of the conversation
        username (str, optional): The username to filter by. Defaults to None (kept for API compatibility).
        email (str, optional): The email to filter by. Defaults to None (kept for API compatibility).

    Returns:
        bool: True if the conversation is marked as a good case, False otherwise
    """
    logger.info(f"Checking if conversation {conversation_id} is a good case")
    # Note: username and email parameters are kept for API compatibility but not used
    # since the good_case table design doesn't require user filtering
    return get_conversation_good_case_status(conversation_id)
