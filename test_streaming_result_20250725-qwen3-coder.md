# 流式架构测试报告 - 20250725

## 测试概要

- **测试时间**: 2025-07-25 13:42:29
- **测试用例总数**: 12
- **通过测试**: 12
- **失败测试**: 0
- **通过率**: 100.0%
- **Unknown column错误**: 2
- **总耗时**: 567.63秒
- **并发数**: 3

## Agent统计


### sales_order_analytics
- **测试数量**: 7
- **通过数量**: 7
- **失败数量**: 0
- **通过率**: 100.0%
- **Unknown column错误**: 2
- **平均耗时**: 171.18秒


### warehouse_and_fulfillment
- **测试数量**: 3
- **通过数量**: 3
- **失败数量**: 0
- **通过率**: 100.0%
- **Unknown column错误**: 0
- **平均耗时**: 56.33秒


### sales_kpi_analytics
- **测试数量**: 1
- **通过数量**: 1
- **失败数量**: 0
- **通过率**: 100.0%
- **Unknown column错误**: 0
- **平均耗时**: 65.68秒


### general_chat_bot
- **测试数量**: 1
- **通过数量**: 1
- **失败数量**: 0
- **通过率**: 100.0%
- **Unknown column错误**: 0
- **平均耗时**: 25.61秒

## 详细测试结果


### ✅ 测试 1: 测试sales_order_analytics - 新门店注册分析

**查询内容**:
```
杭州市昨天新增多少新注册门店？
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 35.63秒
**响应数量**: 53
**会话ID**: `test_conv_1753421582_1`

**完整对话记录**:
**1. 用户**: 杭州市昨天新增多少新注册门店？

**⚠️错误信息**: Unknown column found

**2. 助手**: 
杭州市昨天新增多少新注册门店？
___

请先查找merchant表的DDL语句，然后查询杭州市昨天新增多少新注册门店
___
根据查询结果，**杭州市昨天（2025年7月24日）新增了11家新注册门店**。

这个数据是基于merchant表中的注册时间（register_time）字段统计的，查询条件包括：
- 城市为杭州市
- 注册时间在2025年7月24日当天
- 门店状态正常（islock = 0，即未锁定状态）

这11家新注册门店为杭州市的商户网络扩张做出了贡献。


---


### ✅ 测试 2: 测试sales_order_analytics - 有效拉新判断

**查询内容**:
```
为什么客户 TeaBaby茶宝宝 (ID: 559867) 不算拉新？
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 376.91秒
**响应数量**: 365
**会话ID**: `test_conv_1753421582_2`

**完整对话记录**:
**1. 用户**: 为什么客户 TeaBaby茶宝宝 (ID: 559867) 不算拉新？

**⚠️错误信息**: Unknown column found

**2. 助手**: 
为什么客户 TeaBaby茶宝宝 (ID: 559867) 不算拉新？请分析这个客户的拉新判定逻辑，包括：
1. 门店首笔订单时间与BD私海时间对比
2. 门店注册方式（是否扫BD地推码）
3. BD拜访记录情况
4. 订单金额是否达到拉新门槛
请提供详细的判定过程和原因分析。
___
让我先查看相关表的结构，然后重新编写SQL：
请先查看merchant表和follow_up_relation表的DDL结构，然后分析客户TeaBaby茶宝宝(ID: 559867)的拉新判定情况。需要分析：
1. 商户注册时间和方式
2. BD私海关系
3. 首笔订单情况
4. 拜访记录
5. 拉新判定逻辑
___
现在让我查询该商户的BD关系和订单情况：
继续分析TeaBaby茶宝宝(ID: 559867)的拉新情况，查询：
1. BD私海关系历史记录
2. 首笔订单时间和金额
3. 商户注册来源（是否扫码注册）
4. BD拜访记录
请使用正确的表结构编写SQL查询
___
现在查询该商户的首笔订单情况：
查询TeaBaby茶宝宝(ID: 559867)的首笔订单情况和所有订单记录，分析订单时间与BD私海时间的关系
___
现在查询该商户的注册来源和拜访记录：
查询TeaBaby茶宝宝(ID: 559867)的注册来源（是否扫BD地推码）和BD拜访记录，分析拉新判定逻辑
___
现在让我查询该商户所有订单的详细情况，以分析拉新判定：
查询TeaBaby茶宝宝(ID: 559867)的所有订单记录，分析订单金额是否达到拉新门槛（15元），并结合BD私海时间进行拉新判定分析
___
现在让我查询该商户的BD私海关系详细信息：
查询TeaBaby茶宝宝(ID: 559867)的BD私海关系变更历史，分析该客户为什么不算拉新的具体原因
___
让我重新使用正确的字段名查询：
使用正确的字段名重新查询TeaBaby茶宝宝(ID: 559867)的拉新判定分析，包括：
1. BD私海关系历史（使用add_time字段）
2. 订单记录（使用total_price字段）
3. BD拜访记录（使用add_time字段）
请根据DDL结构使用正确的字段名
___

继续查询TeaBaby茶宝宝(ID: 559867)的BD私海关系历史和拜访记录，使用正确的字段名
___

查询TeaBaby茶宝宝(ID: 559867)的BD拜访记录，分析拜访时间与下单时间的关系
___
现在让我查询该商户是否通过BD地推码注册：
查询TeaBaby茶宝宝(ID: 559867)是否通过BD地推码注册，并综合分析该客户不算拉新的具体原因
___
现在让我综合分析拉新判定的逻辑：
综合分析TeaBaby茶宝宝(ID: 559867)不算拉新的原因，基于以下信息进行判定：
1. 商户注册时间：2025-07-19 12:01:51
2. 首笔订单时间：2025-07-19 15:29:13，金额：63元
3. BD私海时间：2025-07-20 11:19:40（任凤珠）
4. 未通过BD地推码注册
5. BD拜访时间：2025-07-21（在下单后）

请按照拉新判定逻辑分析为什么不算拉新
___
让我重新简化查询，分析拉新判定逻辑：
基于已获取的数据，分析TeaBaby茶宝宝(ID: 559867)不算拉新的原因。已知信息：
1. 商户注册时间：2025-07-19 12:01:51
2. 首笔订单时间：2025-07-19 15:29:13，金额：63元
3. BD私海时间：2025-07-20 11:19:40（任凤珠）
4. 未通过BD地推码注册
5. BD拜访时间：2025-07-21（在下单后）

请按照拉新判定逻辑分析为什么不算拉新，并给出最终结论。
___
让我重新使用正确的字段名进行分析：
基于已获取的数据，最终分析TeaBaby茶宝宝(ID: 559867)不算拉新的具体原因。根据拉新判定逻辑和已知信息进行判断。
___
现在让我最终分析拉新判定逻辑：
最终分析TeaBaby茶宝宝(ID: 559867)不算拉新的原因。根据已获取的完整信息：
1. 商户注册时间：2025-07-19 12:01:51
2. 首笔订单时间：2025-07-19 15:29:13，金额：63元（>15元）
3. BD私海时间：2025-07-20 11:19:40（任凤珠）
4. 未通过BD地推码注册（merchant_leads表中无记录）
5. BD拜访时间：2025-07-21（在下单后2天）

请按照拉新判定逻辑给出最终结论。
___
根据我对TeaBaby茶宝宝(ID: 559867)的详细分析，该客户不算拉新的原因如下：

## 📊 拉新判定分析结果

### ...


---


### ✅ 测试 3: 测试sales_order_analytics - PB商品销售额分析

**查询内容**:
```
分析一下杭州市的客户昨天PB商品销售额
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 53.13秒
**响应数量**: 109
**会话ID**: `test_conv_1753421582_3`

**完整对话记录**:
**1. 用户**: 分析一下杭州市的客户昨天PB商品销售额

**2. 助手**: 
分析一下杭州市的客户昨天PB商品销售额
___
根据查询结果，我为您分析了杭州市客户昨天（2025年7月24日）PB商品的销售情况：

## 📊 杭州市PB商品销售分析

### 销售总额
**昨天杭州市客户PB商品销售额：20,654.13元**

### 分析说明
- **查询范围**：杭州市注册的客户
- **时间范围**：2025年7月24日全天
- **商品类型**：PB商品（私有品牌），包括：
  - C味
  - Protag蛋白标签
  - SUMMERFARM
  - ZILIULIU
  - 沐清友
  - 澄善
  - 酷盖
  - 鲜沐农场

### 业务解读
杭州市作为重要的市场区域，昨天PB商品销售额超过2万元，显示了私有品牌在当地市场的良好表现。PB商品通常具有更高的毛利率，对公司盈利能力有积极贡献。

这个销售数据反映了杭州市客户对鲜沐自有品牌产品的认可度和购买力。


---


### ✅ 测试 4: 测试sales_order_analytics - 订单明细查询

**查询内容**:
```
查询大客户ID=1132155的门店，本月份的订单明细。列出大客户名字、门店名字和ID、门店地址、订单号、订单金额、商品名字、商品SKU、商品金额、商品件数、下单日
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 65.85秒
**响应数量**: 275
**会话ID**: `test_conv_1753421582_4`

**完整对话记录**:
**1. 用户**: 查询大客户ID=1132155的门店，本月份的订单明细。列出大客户名字、门店名字和ID、门店地址、订单号、订单金额、商品名字、商品SKU、商品金额、商品件数、下单日

**2. 助手**: 
查询大客户ID=1132155的门店，本月份的订单明细。列出大客户名字、门店名字和ID、门店地址、订单号、订单金额、商品名字、商品SKU、商品金额、商品件数、下单日
___
根据查询结果，我为您找到了大客户ID=1132155（O2）的门店在本月份（2025年7月）的订单明细。以下是详细信息：

## 查询结果概览

**大客户名称**：O2  
**查询时间范围**：2025年7月1日-7月25日  
**订单总数**：79条订单明细记录  
**涉及门店数量**：3家门店

## 门店信息

### 1. O2平湖吾悦（门店ID：445875）
- **地址**：浙江嘉兴市平湖市南市路811号
- **订单数量**：3条明细记录
- **订单日期**：7月17日

### 2. 南湖天地O2（门店ID：525309）
- **地址**：浙江嘉兴市平湖市当湖街道南市路811号O2鲜榨果汁(嘉兴平湖店)
- **订单数量**：4条明细记录
- **订单日期**：7月13日、7月19日

### 3. O2鲜榨果汁中山路八佰伴（门店ID：555789）
- **地址**：浙江嘉兴市南湖区中山路1360号 嘉兴八佰伴(中山路店)
- **订单数量**：72条明细记录
- **订单日期**：7月6日-7月21日（活跃度最高）

## 主要商品类型

从订单明细可以看出，O2门店主要采购的商品包括：
- **水果类**：硒砂瓜、陕西红富士苹果、湖北夏橙、砀山梨、台农芒果、浙江巨峰葡萄等
- **蔬菜类**：胡萝卜、黄瓜、羽衣甘蓝等
- **特色商品**：即食秘鲁牛油果、紫香1号百香果、杨桃等

## 订单金额分析

- **单笔订单金额范围**：53.00元 - 3,106.67元
- **大额订单**：O2鲜榨果汁中山路八佰伴门店的订单0125KS9DCY0717182409金额最高，达到3,106.67元

⚠️ **注意**：由于您的飞书登录状态已过期，完整的订单明细数据无法上传至飞书多维表格。建议您重新登录飞书系统后再次查询，以获取完整的数据表格。

这些数据显示O2作为鲜榨果汁连锁品牌，主要采购新鲜水果和蔬菜用于制作饮品，其中中山路八佰伴门店是最活跃的采购门店。


---


### ✅ 测试 5: 测试sales_order_analytics - 履约订单明细查询

**查询内容**:
```
查询大客户ID=1132155的门店，本月份的履约订单明细
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 76.77秒
**响应数量**: 240
**会话ID**: `test_conv_1753421582_5`

**完整对话记录**:
**1. 用户**: 查询大客户ID=1132155的门店，本月份的履约订单明细

**2. 助手**: 
查询大客户ID=1132155的门店，本月份的履约订单明细
___
根据查询结果，我为您找到了大客户ID=1132155下门店的本月份履约订单明细。以下是详细信息：

## 查询结果概览

**查询范围**：2025年7月份（本月）
**履约状态**：已完成配送（status=6）
**总记录数**：79条履约明细

## 门店分布

查询到该大客户下有3家门店：
1. **O2鲜榨果汁中山路八佰伴**（门店ID: 555789）- 主要门店，占大部分订单
2. **南湖天地O2**（门店ID:525309）
3. **O2平湖吾悦**（门店ID:445875）

## 主要履约数据

### 订单时间分布
- 最早订单：2025-07-06
- 最新订单：2025-07-21
- 订单频次：几乎每日都有订单

### 主要商品类型
主要采购的商品包括：
- **水果类**：砀山梨、陕西红富士苹果、湖北夏橙、硒砂瓜、台农芒果、即食秘鲁牛油果等
- **蔬菜类**：羽衣甘蓝、胡萝卜、黄瓜等
- **特色水果**：浙江巨峰葡萄、紫香1号百香果、越南红心火龙果等

### 履约金额分析
- 单笔商品履约金额从6.00元到846.00元不等
- 大额履约商品主要是湖北夏橙、硒砂瓜、陕西红富士苹果等

## 业务特点

1. **业务性质**：从商品结构看，这是鲜榨果汁连锁店，主要采购各类新鲜水果和蔬菜
2. **采购频次**：采购频次很高，几乎每日都有订单
3. **商品多样性**：商品种类丰富，涵盖多种时令水果和蔬菜
4. **履约及时性**：所有查询的订单都已完成配送，履约状况良好

**注意**：由于飞书登录状态过期，完整的数据表格无法上传到飞书文档。如需查看完整的79条履约明细，请重新登录飞书系统后再次查询。


---


### ✅ 测试 6: 测试sales_order_analytics - 销售额对比分析

**查询内容**:
```
查询一下M1陈忠良的团队，昨天共售卖了多少流量品？按照商品名称和商品SKU分组，列出商品名字、商品SKU、商品金额、商品件数、下单日
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 88.50秒
**响应数量**: 226
**会话ID**: `test_conv_1753421617_6`

**完整对话记录**:
**1. 用户**: 查询一下M1陈忠良的团队，昨天共售卖了多少流量品？按照商品名称和商品SKU分组，列出商品名字、商品SKU、商品金额、商品件数、下单日

**2. 助手**: 
什么是流量品？流量品的定义是什么？
___

查询一下M1陈忠良的团队，昨天（2025-07-24）共售卖了多少流量品？按照商品名称和商品SKU分组，列出商品名字、商品SKU、商品金额、商品件数、下单日。请先查找相关表的DDL语句，然后参考DDL语句编写准确、高效的SQL来解答问题，一定要反复确认SQL是否使用了最合理的索引。
___
根据查询结果，M1陈忠良的团队昨天（2025-07-24）共售卖了29种流量品，总金额为**6,673.36元**，总件数为**113件**。

## 详细销售明细

以下是按商品金额从高到低排序的流量品销售情况：

| 商品名字 | 商品SKU | 商品金额(元) | 商品件数 | 下单日 |
|---------|---------|-------------|----------|--------|
| 酷盖纯牛奶 | 607330063305 | 2,609.64 | 46 | 2025-07-24 |
| 小台农芒果冻肉 | 780484374027 | 801.53 | 4 | 2025-07-24 |
| 澄善金枕无核榴莲冻肉 | 780103418648 | 774.24 | 3 | 2025-07-24 |
| C味三色大芋圆 | 661210374451 | 584.00 | 4 | 2025-07-24 |
| Protag纯牛奶 | 607164503701 | 280.00 | 4 | 2025-07-24 |
| C味马蹄爆爆珠 | 671544803237 | 270.00 | 2 | 2025-07-24 |
| C味荔浦芋头块 | 777887102237 | 220.00 | 1 | 2025-07-24 |
| C味调制芋泥（荔浦） | 776581274350 | 155.00 | 1 | 2025-07-24 |
| 澄善蓝莓碎果馅 |852414437460 | 132.80 | 3 | 2025-07-24 |
| Protag常温生椰乳 | 1007565354171 | 125.00 | 1 | 2025-07-24 |

## 业务分析

1. **主力产品**：酷盖纯牛奶是昨天的销售冠军，单品贡献了2,609.64元，占总销售额的39.1%
2. **品牌分布**：流量品主要集中在C味、澄善、酷盖、Protag等自有品牌
3. **产品类型**：主要包括乳制品、果肉冻品、芋圆配料、果酱等烘焙和饮品原料

**注意**：查询结果显示您的飞书登录状态已过期，如需查看完整的数据表格，请重新登录飞书系统。


---


### ✅ 测试 7: 测试warehouse_and_fulfillment - 质检报告查询

**查询内容**:
```
帮我查一下SKU是 '三麟苏打汽水' 的商品最新的质检报告链接
```

**预期Agent**: `warehouse_and_fulfillment`
**实际Agent**: `warehouse_and_fulfillment`
**状态**: `passed`
**耗时**: 63.03秒
**响应数量**: 144
**会话ID**: `test_conv_1753421635_7`

**完整对话记录**:
**1. 用户**: 帮我查一下SKU是 '三麟苏打汽水' 的商品最新的质检报告链接

**2. 助手**: 
三麟苏打汽水
___
我找到了3个三麟苏打汽水的商品，请确认您要查询质检报告的是哪一个：

1. **三麟苏打汽水** (SKU: 823810675874) - 335mL*24瓶
2. **三麟苏打汽水-白桃味** (SKU: 824216557567) - 335mL*24瓶/白桃/无  
3. **三麟苏打汽水-柠檬味** (SKU: 824345122148) - 335mL*24瓶/柠檬/无

请回复数字选择，或者如果您指的是第一个原味的三麟苏打汽水，我将为您查询SKU为823810675874的商品质检报告。
查询SKU为823810675874的三麟苏打汽水商品的最新质检报告链接
___
## 查询结果

我已经为您查询到了SKU为823810675874的三麟苏打汽水商品的最新质检报告信息：

### 商品信息
- **商品名称**：三麟苏打汽水
- **SKU**：823810675874
- **规格**：335mL*24瓶
- **批次**：20250721112536131
- **生产日期**：2025-06-19
- **保质期**：2026-12-19
- **报告创建时间**：2025-07-23 11:17:52

### 质检报告链接
🔗 **最新质检报告链接**：[https://azure.summerfarm.net/kc3rqs28eknfe808q.jpg](https://azure.summerfarm.net/kc3rqs28eknfe808q.jpg)

这是该商品最新批次的质检报告，您可以点击链接查看详细的质检报告内容。报告是在2025年7月23日创建的，对应的是6月19日生产的批次。


---


### ✅ 测试 8: 测试sales_order_analytics - 销售额对比分析

**查询内容**:
```
对比一下'象牌苏打水', '三麟苏打汽水'在过去2个月的销售情况
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 501.43秒
**响应数量**: 294
**会话ID**: `test_conv_1753421648_8`

**完整对话记录**:
**1. 用户**: 对比一下'象牌苏打水', '三麟苏打汽水'在过去2个月的销售情况

**2. 助手**: 
象牌苏打水
___

三麟苏打汽水
___
我找到了相关商品，现在让我调用销售订单分析工具来对比这两个品牌在过去2个月的销售情况：
对比分析'象牌苏打水'和'三麟苏打汽水'在过去2个月（2025年5月25日至2025年7月25日）的销售情况，包括销售额、订单量、销售件数等关键指标。

象牌苏打水相关SKU：
- 6151406134: 象牌苏打水 325mL*24瓶(多多买菜专用)
- 6151406441: 象牌苏打水 325mL*6瓶  
- 6151406635: 象牌苏打水 325mL*24瓶
- 17944351550: 象牌苏打水(柠檬味) 325mL*24瓶

三麟苏打汽水相关SKU：
- 823810675874: 三麟苏打汽水 335mL*24瓶
- 824216557567: 三麟苏打汽水-白桃味 335mL*24瓶
- 824345122148: 三麟苏打汽水-柠檬味 335mL*24瓶

请提供详细的销售数据对比分析。
___
现在让我进一步分析各个SKU的详细销售情况：
查询象牌苏打水和三麟苏打汽水各个SKU在过去2个月（2025年5月25日至2025年7月25日）的详细销售数据，包括每个SKU的商品名称、规格、销售额、订单量、销售件数。

需要查询的SKU：
象牌苏打水：6151406134, 6151406441, 6151406635, 17944351550
三麟苏打汽水：823810675874, 824216557567, 824345122148
___
让我再查询一下月度趋势对比：
查询象牌苏打水和三麟苏打汽水在过去2个月按月份的销售趋势对比，分别统计2025年6月和2025年7月（截至7月25日）的销售数据。

需要查询的SKU：
象牌苏打水：6151406134, 6151406441, 6151406635, 17944351550
三麟苏打汽水：823810675874, 824216557567, 824345122148
___
## 象牌苏打水 vs 三麟苏打汽水 销售对比分析

### 📊 总体销售对比（过去2个月）

| 商品品牌 | 销售额（元） | 订单量 | 销售件数 | 市场占有率 |
|---------|-------------|--------|----------|-----------|
| **象牌苏打水** | 1,395,470.62 | 9,470 | 9,471 | **81.9%** |
| **三麟苏打汽水** | 307,013.86 | 3,474 | 3,474 | **18.1%** |

### 🔍 关键发现

**1. 市场表现差距显著**
- 象牌苏打水的销售额是三麟苏打汽水的**4.5倍**
- 象牌苏打水的订单量是三麟苏打汽水的**2.7倍**
- 象牌苏打水在苏打水市场占据绝对优势地位

**2. 主力产品分析**
- **象牌苏打水**：主力产品为325mL*24瓶规格（SKU:6151406635），贡献了几乎全部销售额
- **三麟苏打汽水**：主要销售335mL*24瓶原味产品（SKU:823810675874）

### 📈 月度趋势对比

| 月份 | 象牌苏打水销售额 | 三麟苏打汽水销售额 | 象牌增长率 | 三麟增长率 |
|------|----------------|------------------|-----------|-----------|
| 2025年6月 | 596,548.00元 | 139,622.29元 | - | - |
| 2025年7月 | 638,293.84元 | 135,874.86元 | **+7.0%** | **-2.7%** |

### 💡 业务洞察

**象牌苏打水优势：**
- 市场认知度高，品牌影响力强
- 产品规格多样化，满足不同需求
- 7月份销售额持续增长，显示良好发展势头

**三麟苏打汽水特点：**
- 市场份额相对较小，但有一定的忠实客户群
-7月份销售略有下降，需要关注市场策略

**建议：**1. 象牌苏打水可以继续巩固市场领先地位，考虑推出更多口味和规格
2. 三麟苏打汽水需要加强市场推广，提升品牌知名度
3. 两个品牌都属于碳酸饮料类目，可以考虑差异化定位策略


---


### ✅ 测试 9: 测试sales_kpi_analytics - 销售团队绩效分析

**查询内容**:
```
M1 李钱程团队本月的绩效分析
```

**预期Agent**: `sales_kpi_analytics`
**实际Agent**: `sales_kpi_analytics`
**状态**: `passed`
**耗时**: 65.68秒
**响应数量**: 249
**会话ID**: `test_conv_1753421659_9`

**完整对话记录**:
**1. 用户**: M1 李钱程团队本月的绩效分析

**2. 助手**: 
M1 李钱程团队本月的绩效分析
___
## M1 李钱程团队本月绩效分析

根据查询结果，李钱程团队（包含6名成员）在2025年7月的绩效表现如下：

### 团队成员构成
- **李钱程**（团队负责人）
- **许萧余**、**杨春福**、**严红坤**、**袁自超**、**徐俊杰**

### 绩效排名（按佣金总额排序）

| BD姓名 | 佣金总金额 | 高价值客户数 | 超额SPU数 | MTD履约GMV | 商品毛利润 |
|--------|------------|--------------|-----------|------------|------------|
| 许萧余 | 6,163.12元 | 92个 | 702个 | 630,231.01元 | 122,850.89元 |
| 杨春福 | 5,854.16元 | 83个 | 655个 | 576,994.88元 | 105,210.15元 |
| 严红坤 | 4,719.18元 | 76个 | 683个 | 580,625.27元 | 99,306.57元 |
| 袁自超 | 4,647.87元 | 81个 | 512个 | 599,039.64元 | 93,945.46元 |
| 徐俊杰 | 4,341.85元 | 77个 | 565个 | 538,432.76元 | 124,870.60元 |
| 李钱程 | 0.00元 | 0个 | 0个 | 0.00元 | 0.00元 |

### 关键绩效指标分析

**团队总体表现：**
- **总佣金**：25,726.18元（不含李钱程）
- **高价值客户总数**：409个
- **超额SPU总数**：3,117个
- **团队履约GMV**：2,925,323.56元
- **团队毛利润**：546,183.67元

**个人表现亮点：**
1. **许萧余** - 团队佣金冠军，高价值客户数最多（92个）
2. **杨春福** - 佣金第二，利润积分最高（7,076.38分）
3. **徐俊杰** - 商品毛利润最高（124,870.60元）

**需要关注的问题：**
- **李钱程**作为团队负责人，本月个人业绩为零，可能专注于团队管理工作
- 团队成员间业绩差距较大，最高与最低相差约6,000元佣金

### 业务建议
1. 继续发挥许萧余在高价值客户开发方面的优势
2. 学习徐俊杰在商品毛利润提升方面的经验
3. 加强团队协作，缩小成员间业绩差距
4. 关注李钱程的业务参与度，平衡管理与业务发展


---


### ✅ 测试 10: 测试warehouse_and_fulfillment - 库存分析

**查询内容**:
```
安佳淡奶油在嘉兴仓的库存分析
```

**预期Agent**: `warehouse_and_fulfillment`
**实际Agent**: `warehouse_and_fulfillment`
**状态**: `passed`
**耗时**: 55.35秒
**响应数量**: 155
**会话ID**: `test_conv_1753421698_10`

**完整对话记录**:
**1. 用户**: 安佳淡奶油在嘉兴仓的库存分析

**2. 助手**: 
安佳淡奶油在嘉兴仓的库存分析
___
## 安佳淡奶油在嘉兴仓库存分析

根据查询结果，嘉兴总仓目前有3种安佳淡奶油产品的库存：

### 📊 库存详情

| 商品名称 | SKU | 规格 | 仓库名称 | 可售库存 |
|---------|-----|------|----------|----------|
| 安佳淡奶油 | N001S01R005 | 1L*12盒 | 嘉兴总仓 | 5,391 |
| 安佳淡奶油 | 56143 | 1L*1盒 | 嘉兴总仓 | 44 |
| 安佳淡奶油10L | 425475253 | 10L*1包 | 嘉兴总仓 | 122 |

### 📈 库存分析

**库存状况总结：**
- **主力产品**：1L*12盒装的安佳淡奶油库存最充足，有5,391盒，是主要的销售规格
- **零售规格**：1L*1盒装的库存较少，仅44盒，适合小批量采购客户
- **大包装**：10L*1包装有122包库存，适合大型餐饮客户

**业务建议：**
1. **补货提醒**：1L*1盒装库存偏低，建议关注补货需求
2. **销售重点**：1L*12盒装库存充足，可作为主推产品
3. **客户匹配**：根据不同规格库存情况，合理匹配客户需求

所有安佳淡奶油产品在嘉兴总仓都有现货，能够满足正常的销售需求。


---


### ✅ 测试 11: 测试warehouse_and_fulfillment - 库存查询

**查询内容**:
```
你好，黑海盗纯牛奶什么时候有货，东莞仓
```

**预期Agent**: `warehouse_and_fulfillment`
**实际Agent**: `warehouse_and_fulfillment`
**状态**: `passed`
**耗时**: 50.60秒
**响应数量**: 168
**会话ID**: `test_conv_1753421706_11`

**完整对话记录**:
**1. 用户**: 你好，黑海盗纯牛奶什么时候有货，东莞仓

**2. 助手**: 
黑海盗纯牛奶
___
找到了黑海盗纯牛奶的商品信息，有两个规格：
1. 黑海盗纯牛奶1L*1盒 (SKU: 607745051057)
2. 黑海盗纯牛奶 1L*12盒 (SKU: 607745051124)

现在我来查询东莞仓的库存和到货情况：
查询黑海盗纯牛奶在东莞仓的库存情况和到货时间，包括两个规格：
1. 黑海盗纯牛奶 1L*1盒 (SKU: 607745051057)
2. 黑海盗纯牛奶 1L*12盒 (SKU: 607745051124)

请查询东莞仓的现有库存、在途库存和预计到货时间
___
很抱歉，我没有在搜索结果中找到"黑海盗纯牛奶"这个品牌的商品。从搜索结果来看，系统中有很多其他品牌的纯牛奶，如雀巢、蒙牛、伊利、酷盖、Protag等，但没有"黑海盗"这个品牌。

**可能的情况：**
1. 商品名称可能有误，建议您确认一下具体的品牌名称
2. 该商品可能暂时不在我们的商品库中
3. 可能是其他相似名称的商品

**建议：**
- 请确认一下是否是其他品牌的纯牛奶，比如"酷盖纯牛奶"或其他品牌
- 如果您能提供更准确的商品名称或SKU，我可以帮您查询具体的库存和到货情况

**东莞仓信息：**
我查询到东莞有两个仓库：
- 东莞总仓（仓库编号：69）
- 东莞冷冻总仓（仓库编号：117）

如果您能确认具体的商品信息，我可以立即为您查询库存和到货时间。


---


### ✅ 测试 12: 测试general_chat_bot - 知识问答

**查询内容**:
```
如何申请DMS账户
```

**预期Agent**: `general_chat_bot`
**实际Agent**: `general_chat_bot`
**状态**: `passed`
**耗时**: 25.61秒
**响应数量**: 82
**会话ID**: `test_conv_1753421725_12`

**完整对话记录**:
**1. 用户**: 如何申请DMS账户

**2. 助手**: 
如何申请DMS账户
___
抱歉，目前我无法访问飞书文档来获取关于DMS账户申请的具体流程信息。

**建议您通过以下方式获取DMS账户申请信息：**

1. **联系IT支持部门** - 他们通常负责数据库管理系统的账户申请和权限管理
2. **咨询直属上级** - 了解部门内的申请流程和审批要求
3. **查看公司内部文档** - 在飞书或其他内部平台搜索相关的申请流程文档
4. **联系DBA团队** - 数据库管理员通常负责DMS账户的创建和权限分配

如果您有其他关于销售数据、仓储物流或商品信息的问题，我很乐意为您提供帮助！


---

