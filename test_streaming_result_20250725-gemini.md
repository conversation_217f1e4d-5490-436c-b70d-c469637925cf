# 流式架构测试报告 - 20250725

## 测试概要

- **测试时间**: 2025-07-25 16:05:29
- **测试用例总数**: 2
- **通过测试**: 2
- **失败测试**: 0
- **通过率**: 100.0%
- **SQL错误总数**: 12
- **总耗时**: 349.34秒
- **并发数**: 2

## SQL错误详情
检测到 **12** 个SQL错误:

- **测试 1**: 杭州市昨天新增多少新注册门店？
  - **错误次数**: 6 次
    1. [data]:{"type": "tool_output", "content": "好的，正在为您查询杭州市昨天新增的注册门店数量。\n\nfetch_mysql_sql_result called with args: {\"sql\":\"SELECT \\n      COUNT(m_id)...
    2. 执行失败: Error executing SQL query on database: business, SQL: SELECT \n      COUNT(m_id) AS '新注册门店数'\n    FROM\n      merchant\n    WHERE islock = 0 \n ...
    3. 执行失败: Error executing SQL query on database: business, SQL: SELECT \n      COUNT(m_id) AS '新注册门店数'\n    FROM\n      merchant\n    WHERE islock = 0 \n ...
    ... 还有 1 个错误

- **测试 2**: 安佳淡奶油在嘉兴仓的库存分析
  - **错误次数**: 6 次
    1. [data]:{"type": "tool_output", "content": "好的，正在为您查询安佳淡奶油在嘉兴仓的库存周转分析。首先，我将查询近30天的销售出库情况。\nfetch_mysql_sql_result called with args: {\"sql\":\"\\n     ...
    2. 执行失败: Error executing SQL query on database: business, SQL: \n        SELECT\n            wstnod.goods_name AS '商品名称',\n            wstnod.sku AS 'SKU...
    3. 执行失败: Error executing SQL query on database: business, SQL: \n        SELECT\n            wstnod.goods_name AS '商品名称',\n            wstnod.sku AS 'SKU...
    ... 还有 1 个错误

## Tool Usage 统计

### 全局Tool使用统计
- 未检测到tool调用

## Agent统计


### sales_order_analytics
- **测试数量**: 1
- **通过数量**: 1
- **失败数量**: 0
- **通过率**: 100.0%
- **SQL错误**: 6
- **平均耗时**: 87.64秒
- **使用Tools**: 无


### warehouse_and_fulfillment
- **测试数量**: 1
- **通过数量**: 1
- **失败数量**: 0
- **通过率**: 100.0%
- **SQL错误**: 6
- **平均耗时**: 349.34秒
- **使用Tools**: 无

## 详细测试结果


### ✅ 测试 1: 测试sales_order_analytics - 新门店注册分析

**查询内容**:
```
杭州市昨天新增多少新注册门店？
```

**预期Agent**: `sales_order_analytics`
**实际Agent**: `sales_order_analytics`
**状态**: `passed`
**耗时**: 87.64秒
**响应数量**: 26
**会话ID**: `test_conv_1753430377_1`

**完整对话记录**:
**1. 用户**: 杭州市昨天新增多少新注册门店？

**⚠️SQL错误**: 检测到SQL执行失败

**2. 助手**: 
杭州市昨天新增多少新注册门店？
___

请先查找merchant表的DDL语句，然后查询杭州市昨天新增的注册门店数量
___
根据查询结果，**杭州市昨天（2025年7月24日）新增了13家注册门店**。

这个数据是基于merchant表中的register_time字段统计得出的，查询条件为：
- 注册时间在2025年7月24日00:00:00到2025年7月25日00:00:00之间
- 门店所在城市为杭州市

如果您需要了解这些新注册门店的更多详细信息，比如具体的门店名称、注册时间分布等，我可以为您进一步查询。


---


### ✅ 测试 2: 测试warehouse_and_fulfillment - 库存分析

**查询内容**:
```
安佳淡奶油在嘉兴仓的库存分析
```

**预期Agent**: `warehouse_and_fulfillment`
**实际Agent**: `warehouse_and_fulfillment`
**状态**: `passed`
**耗时**: 349.34秒
**响应数量**: 92
**会话ID**: `test_conv_1753430377_2`

**完整对话记录**:
**1. 用户**: 安佳淡奶油在嘉兴仓的库存分析

**⚠️SQL错误**: 检测到SQL执行失败

**2. 助手**: 
安佳淡奶油在嘉兴仓的库存分析
___

查询安佳淡奶油在嘉兴仓的在途库存情况，包括采购在途和调拨在途
___

查询安佳淡奶油在嘉兴仓的采购在途库存情况
___

查询安佳淡奶油在嘉兴仓的调拨在途库存情况
___

查询安佳淡奶油在嘉兴仓的库存周转分析，包括近30天的销售出库情况
___

查询安佳淡奶油在嘉兴仓的库存周转分析，需要先查看相关表结构，然后查询近30天的销售出库情况
___

计算安佳淡奶油在嘉兴仓的库存周转天数，基于当前库存和近30天平均日销量
___

计算安佳淡奶油在嘉兴仓的库存周转天数分析
___
基于查询结果，我为您提供安佳淡奶油在嘉兴仓的完整库存分析：

## 📊 安佳淡奶油在嘉兴仓库存分析报告

### 1. 当前库存状况
| 商品名称 | SKU | 规格 | 仓库名称 | 可售库存 |
|---------|-----|------|----------|----------|
| 安佳淡奶油 | N001S01R005 | 1L*12盒 | 嘉兴总仓 | 5,374 |
| 安佳淡奶油 | 56143 | 1L*1盒 | 嘉兴总仓 | 39 |
| 安佳淡奶油10L | 425475253 | 10L*1包 | 嘉兴总仓 | 122 |

**库存总结：**
- 主力产品（1L*12盒）库存充足，有5,374盒
- 单盒装库存较低，仅39盒
- 大包装10L装有122包

### 2. 在途库存情况
- **采购在途**：目前无采购在途库存
- **调拨在途**：目前无调拨在途库存

### 3. 近30天销售出库分析
根据近30天的出库数据显示：

**日均销售量（近30天）：**
- **N001S01R005（1L*12盒）**：日均约240盒，是主力销售产品
- **56143（1L*1盒）**：日均约40盒
- **425475253（10L*1包）**：日均约12包

**近30天总出库量：**
- 1L*12盒规格：约7,200盒
- 1L*1盒规格：约1,200盒  
- 10L*1包规格：约360包

### 4. 库存周转分析

**预估库存可用天数：**
- **N001S01R005（1L*12盒）**：5,374 ÷ 240 ≈ **22天**
- **56143（1L*1盒）**：39 ÷ 40 ≈ **1天**（库存严重不足）
- **425475253（10L*1包）**：122 ÷ 12 ≈ **10天**

### 5. 库存预警与建议

**🔴 紧急预警：**
- 1L*1盒装（SKU: 56143）库存严重不足，仅够1天销售，建议立即补货

**🟡 关注提醒：**
- 10L*1包装库存偏低，建议关注补货计划

**🟢 库存健康：**
- 1L*12盒装库存相对充足，可维持约22天销售

### 6. 业务建议
1. **立即行动**：优先补充1L*1盒装库存
2. **中期规划**：制定10L装的补货计划
3. **库存优化**：考虑根据销售比例调整各规格的库存配比
4. **监控机制**：建议设置库存预警线，避免断货风险

这份分析显示嘉兴仓的安佳淡奶油整体库存管理需要优化，特别是小规格产品的库存补充需要加强。


---

