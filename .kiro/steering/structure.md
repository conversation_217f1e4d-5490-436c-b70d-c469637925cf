# Project Structure & Organization

## Root Directory Layout

```
chatbi/
├── src/                    # Main source code (DDD architecture)
├── resources/              # Configuration files and static resources
├── tests/                  # Test files
├── docs/                   # Documentation
├── logs/                   # Application logs
├── app.py                  # Flask application entry point
├── pyproject.toml          # Python project configuration
├── Dockerfile              # Container configuration
└── docker-compose.yml      # Local development stack
```

## Source Code Organization (src/)

### API Layer (`src/api/`)
- **Purpose**: Flask blueprints for HTTP endpoints
- **Pattern**: One file per API domain (auth, query, dashboard, etc.)
- **Files**: `auth_api.py`, `query_api.py`, `dashboard_api.py`, `resource_api.py`

### Services Layer (`src/services/`)
- **Purpose**: Business logic and domain services
- **Architecture**: Domain-driven design with clear boundaries

#### Agent Services (`src/services/agent/`)
- **bots/**: Agent implementations (CoordinatorBot, DataFetcherBot)
- **tools/**: Shared tools for agents (data_tools, ddl_tools, feishu_tools)
- **utils/**: Agent-specific utilities (model_provider, formatter)

#### Authentication Services (`src/services/auth/`)
- **Purpose**: User authentication and session management
- **Key Files**: `user_login_with_feishu.py`, `token_refresh_service.py`

#### Feishu Integration (`src/services/feishu/`)
- **Purpose**: Feishu bot and API integration
- **Architecture**: Modular design with separate concerns
- **Key Files**: `client.py`, `event_handlers.py`, `message_core.py`

### Data Access Layer (`src/repositories/`)
- **Purpose**: Data access abstraction (Repository pattern)
- **Structure**: Organized by database (chatbi/, xianmudb/)
- **Pattern**: One repository per aggregate root

### Database Layer (`src/db/`)
- **Purpose**: Database connections and query execution
- **Files**: `connection.py`, `query.py`, `database_enum.py`

### Models (`src/models/`)
- **Purpose**: Data transfer objects and domain models
- **Files**: `user_session.py`, `query_result.py`, `user_info_class.py`

### Utilities (`src/utils/`)
- **Purpose**: Cross-cutting concerns and helpers
- **Files**: `logger.py`, `image_utils.py`, `conversation_utils.py`

## Resources Directory (`resources/`)

### Agent Configuration (`resources/data_fetcher_bot_config/`)
- **Purpose**: YAML configuration files for specialized agents
- **Pattern**: One YAML file per agent type
- **Examples**: `sales_order_analytics.yml`, `warehouse_and_fulfillment.yml`

### Prompts (`resources/prompt/`)
- **Purpose**: System prompts and instructions for LLMs
- **Pattern**: Markdown files with agent instructions

### Database Schemas (`resources/tables_ddl/`)
- **Purpose**: DDL definitions for business database tables
- **Pattern**: One SQL file per table

## Frontend Assets (`src/static/`)

### CSS Organization (`src/static/css/`)
```
css/
├── base/           # Base styles (theme, animations, fonts)
├── components/     # Component-specific styles
│   ├── chatbi/     # Chat interface components
│   ├── common/     # Reusable UI components
│   └── dashboard/  # Dashboard-specific components
└── layouts/        # Layout-specific styles
```

### JavaScript Organization (`src/static/js/`)
```
js/
├── chatbi/         # Chat interface logic
│   ├── components/ # Vue-like components
│   ├── composables/# Reusable logic
│   └── services/   # API services
├── dashboard/      # Dashboard functionality
├── common/         # Shared components
└── utils/          # Utility functions
```

## Naming Conventions

### Files and Directories
- **Snake_case**: For Python files and directories
- **Kebab-case**: For CSS and JS files
- **PascalCase**: For JavaScript components

### Python Code
- **Classes**: PascalCase (`UserSession`, `CoordinatorBot`)
- **Functions/Variables**: snake_case (`get_user_info`, `user_session`)
- **Constants**: UPPER_SNAKE_CASE (`DATABASE_URL`, `MAX_RETRIES`)

### Database
- **Tables**: snake_case (`chat_history`, `user_sessions`)
- **Columns**: snake_case (`user_id`, `created_at`)

## Module Import Patterns

### Absolute Imports
```python
from src.services.agent import run_agent_query
from src.db.connection import execute_db_query
from src.models.user_session import UserSession
```

### Service Layer Imports
```python
# Import from service modules, not implementation details
from src.services.auth.user_session_service import get_user_session
# Not: from src.repositories.chatbi.user_session import UserSessionRepository
```

## Configuration Management

### Environment-based Configuration
- **Development**: `.env` file with local settings
- **Production**: Environment variables in container/deployment
- **Secrets**: Never commit sensitive data to version control

### Agent Configuration
- **YAML Files**: Declarative agent behavior configuration
- **Hot Reload**: Configuration changes don't require restart
- **Validation**: Config validation on startup

## Testing Structure (`tests/`)

### Organization
```
tests/
├── unit/           # Unit tests for individual components
├── integration/    # Integration tests for service interactions
└── e2e/           # End-to-end tests for complete workflows
```

### Test Naming
- **Files**: `test_<module_name>.py`
- **Classes**: `Test<ClassName>`
- **Methods**: `test_<behavior_description>`

## Documentation (`docs/`)

### Types
- **Architecture**: High-level system design documents
- **Implementation**: Detailed implementation guides
- **API**: API documentation and examples
- **Deployment**: Deployment and operations guides