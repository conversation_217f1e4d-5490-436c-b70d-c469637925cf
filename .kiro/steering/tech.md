# Technology Stack & Build System

## Core Technologies

- **Backend Framework**: <PERSON><PERSON><PERSON> (Python 3.8+)
- **Package Manager**: uv (modern Python package manager)
- **Database**: MySQL 8.0 (dual database architecture)
- **AI Framework**: OpenAI Agents with LiteLLM support
- **Authentication**: Feishu OAuth2.0 + JWT
- **Integration**: Feishu (Lark) Open Platform WebSocket + HTTP APIs
- **Containerization**: Docker with multi-stage builds

## Key Dependencies

```toml
flask>=2.0
lark-oapi>=1.4.14
mysql-connector-python>=8.0
openai>=1.0
openai-agents[litellm]>=0.0.17
pandas>=2.2.3
python-dotenv>=0.19
pyyaml>=6.0
aiohttp>=3.11.18
pyjwt>=2.8.0
```

## Development Tools

- **Linting**: <PERSON>uff (black-compatible formatter)
- **Environment**: Python virtual environments via uv
- **Configuration**: Environment variables with .env files

## Common Commands

### Development Setup
```bash
# Install dependencies
uv sync --locked

# Copy environment template
cp env.example .env

# Start development server
python app.py --port 5700 --debug

# Start without debug
python app.py --port 5700 --no-debug
```

### Docker Operations
```bash
# Build image
docker build -t chatbi .

# Run with docker-compose (includes MySQL)
docker-compose up -d

# View logs
docker-compose logs -f
```

### Database Setup
```bash
# Initialize database (via docker-compose)
docker-compose up mysql

# Database will auto-initialize with init.sql
```

## Architecture Patterns

- **Domain-Driven Design (DDD)**: Clear separation of concerns
- **Agent-as-Tool**: Specialized agents for different business domains
- **Event-Driven**: StreamEvent-based message passing
- **Repository Pattern**: Data access abstraction
- **Connection Pooling**: Optimized database connections

## Configuration

### Environment Variables
```bash
# Feishu Integration
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret

# Database Configuration
CHATBI_MYSQL_HOST=localhost
CHATBI_MYSQL_PORT=3307
BUSINESS_DB_HOST=localhost
BUSINESS_DB_PORT=3306

# Application Settings
APPLICATION_ROOT=/crm-chatbi
ENABLE_BOT_MESSAGE_PROCESSING=true
```

### Agent Configuration
- Agent behaviors defined in YAML files under `resources/data_fetcher_bot_config/`
- System prompts stored in `resources/prompt/`
- Database DDL definitions in `resources/tables_ddl/`